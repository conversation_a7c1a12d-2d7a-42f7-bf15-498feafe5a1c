{% extends "base.html" %}

{% block title %}تعديل الدين - {{ debt.creditor_name }} - مصروف{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <h2 class="text-white mb-4">
            <i class="fas fa-edit me-2"></i>
            تعديل الدين - {{ debt.creditor_name }}
        </h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}" class="text-white">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('debts') }}" class="text-white">الديون</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('debt_details', debt_id=debt.id) }}" class="text-white">{{ debt.creditor_name }}</a></li>
                <li class="breadcrumb-item active text-white">تعديل</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل بيانات الدين
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.creditor_name.label(class="form-label") }}
                            {{ form.creditor_name(class="form-control", placeholder="اسم البنك أو الشخص أو الجهة") }}
                            {% if form.creditor_name.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.creditor_name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.debt_type.label(class="form-label") }}
                            {{ form.debt_type(class="form-select") }}
                            {% if form.debt_type.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.debt_type.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.total_amount.label(class="form-label") }}
                        {{ form.total_amount(class="form-control", placeholder="أدخل المبلغ الإجمالي للدين") }}
                        {% if form.total_amount.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.total_amount.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.monthly_payment.label(class="form-label") }}
                            {{ form.monthly_payment(class="form-control", placeholder="القسط الشهري") }}
                            <small class="text-muted">اختياري</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.installments_count.label(class="form-label") }}
                            {{ form.installments_count(class="form-control", placeholder="عدد الأقساط") }}
                            <small class="text-muted">اختياري</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.monthly_due_date.label(class="form-label") }}
                        {{ form.monthly_due_date(class="form-control", placeholder="مثال: 15 (لليوم 15 من كل شهر)") }}
                        <small class="text-muted">اختياري - يوم الاستحقاق الشهري</small>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.start_date.label(class="form-label") }}
                            {{ form.start_date(class="form-control", type="date") }}
                            <small class="text-muted">اختياري</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.end_date.label(class="form-label") }}
                            {{ form.end_date(class="form-control", type="date") }}
                            <small class="text-muted">اختياري</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3", placeholder="تفاصيل إضافية عن الدين...") }}
                        {% if form.description.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.description.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-2"></i>
                            حفظ التعديلات
                        </button>
                        <a href="{{ url_for('debt_details', debt_id=debt.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            إلغاء والعودة
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="card mt-4">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle me-2 text-info"></i>
                    معلومات مهمة
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-exclamation-triangle text-warning me-2 mt-1"></i>
                            <div>
                                <strong>تنبيه</strong>
                                <p class="text-muted small mb-0">تعديل المبلغ الإجمالي سيؤثر على حساب المبلغ المتبقي</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calculator text-info me-2 mt-1"></i>
                            <div>
                                <strong>إعادة حساب</strong>
                                <p class="text-muted small mb-0">سيتم إعادة حساب المبلغ المتبقي تلقائياً</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // التحقق من صحة البيانات
        const form = document.querySelector('form');
        const totalAmountInput = document.getElementById('total_amount');
        const monthlyPaymentInput = document.getElementById('monthly_payment');
        const installmentsInput = document.getElementById('installments_count');
        
        form.addEventListener('submit', function(e) {
            let isValid = true;
            
            // التحقق من المبلغ الإجمالي
            if (!totalAmountInput.value || parseFloat(totalAmountInput.value) <= 0) {
                alert('يرجى إدخال مبلغ إجمالي صحيح');
                totalAmountInput.focus();
                isValid = false;
            }
            
            // التحقق من القسط الشهري إذا تم إدخاله
            if (monthlyPaymentInput.value && parseFloat(monthlyPaymentInput.value) <= 0) {
                alert('يرجى إدخال قسط شهري صحيح أو تركه فارغاً');
                monthlyPaymentInput.focus();
                isValid = false;
            }
            
            // التحقق من عدد الأقساط إذا تم إدخاله
            if (installmentsInput.value && parseInt(installmentsInput.value) <= 0) {
                alert('يرجى إدخال عدد أقساط صحيح أو تركه فارغاً');
                installmentsInput.focus();
                isValid = false;
            }
            
            if (!isValid) {
                e.preventDefault();
            }
        });
        
        // تنسيق الأرقام
        [totalAmountInput, monthlyPaymentInput].forEach(input => {
            if (input) {
                input.addEventListener('input', function() {
                    // إزالة الأحرف غير الرقمية
                    this.value = this.value.replace(/[^0-9.]/g, '');
                });
            }
        });
    });
</script>
{% endblock %}
