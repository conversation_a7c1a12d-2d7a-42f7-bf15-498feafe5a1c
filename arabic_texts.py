# ملف النصوص العربية المصححة
# Arabic Texts - Corrected Version

class ArabicTexts:
    """فئة تحتوي على جميع النصوص العربية المستخدمة في التطبيق"""
    
    # العناوين الرئيسية
    APP_TITLE = "مصروف - إدارة الرواتب والمصروفات الشخصية"
    SALARY_WINDOW_TITLE = "إدخال الراتب الشهري"
    DASHBOARD_TITLE = "لوحة المعلومات"
    SIDEBAR_TITLE = "القوائم الرئيسية"
    
    # أزرار القوائم الرئيسية
    SALARY_INPUT_BUTTON = "📊 إدخال الراتب"
    EXPENSES_MANAGEMENT_BUTTON = "💰 إدارة المصروفات"
    DEBTS_MANAGEMENT_BUTTON = "📋 إدارة الديون"
    REPORTS_ANALYSIS_BUTTON = "📈 التقارير والتحليل"
    
    # حقول إدخال الراتب
    FIRST_SALARY_LABEL = "الراتب الأول (دينار عراقي):"
    SECOND_SALARY_LABEL = "الراتب الثاني (اختياري):"
    FIRST_SALARY_PLACEHOLDER = "أدخل مبلغ الراتب الأول"
    SECOND_SALARY_PLACEHOLDER = "أدخل مبلغ الراتب الثاني (إن وجد)"
    
    # حقول التاريخ
    MONTH_LABEL = "الشهر:"
    YEAR_LABEL = "السنة:"
    NOTES_LABEL = "ملاحظات (اختياري):"
    
    # أزرار العمليات
    SAVE_SALARY_BUTTON = "حفظ الراتب"
    CLEAR_DATA_BUTTON = "مسح البيانات"
    CANCEL_BUTTON = "إلغاء"
    
    # رسائل النجاح والخطأ
    SUCCESS_TITLE = "تم الحفظ بنجاح"
    ERROR_TITLE = "خطأ"
    SALARY_SAVED_SUCCESS = "تم حفظ الراتب بنجاح!"
    FIRST_SALARY_REQUIRED = "يرجى إدخال مبلغ الراتب الأول"
    INVALID_NUMBERS = "يرجى إدخال أرقام صحيحة للرواتب"
    SAVE_ERROR = "حدث خطأ أثناء الحفظ"
    
    # نصوص لوحة المعلومات
    NO_SALARY_YET = "لم يتم إدخال أي راتب بعد\nاضغط على 'إدخال الراتب' لبدء التسجيل"
    LATEST_SALARY = "آخر راتب مُسجل"
    FIRST_SALARY = "الراتب الأول"
    SECOND_SALARY = "الراتب الثاني"
    TOTAL_SALARY = "المجموع"
    MONTH = "الشهر"
    
    # رسالة الترحيب
    WELCOME_MESSAGE = """مرحباً بك في تطبيق مصروف!

يمكنك الآن:
• إدخال وتتبع رواتبك الشهرية
• إدارة مصروفاتك اليومية
• متابعة الديون والالتزامات
• عرض التقارير والتحليلات المالية

ابدأ بإدخال راتبك الشهري من القائمة الجانبية"""
    
    # رسائل الميزات القادمة
    COMING_SOON = "قريباً"
    EXPENSES_COMING_SOON = "نافذة إدارة المصروفات ستكون متاحة قريباً!"
    DEBTS_COMING_SOON = "نافذة إدارة الديون ستكون متاحة قريباً!"
    REPORTS_COMING_SOON = "نافذة التقارير والتحليل ستكون متاحة قريباً!"
    
    # العملة
    CURRENCY = "دينار عراقي"
    CURRENCY_SHORT = "دينار"
    
    # الأشهر العربية
    MONTHS = [
        "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
        "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
    ]
    
    @staticmethod
    def format_salary_success(first_salary, second_salary, total, month, year):
        """تنسيق رسالة نجاح حفظ الراتب"""
        return f"""{ArabicTexts.SALARY_SAVED_SUCCESS}
{ArabicTexts.FIRST_SALARY}: {first_salary:,.0f} {ArabicTexts.CURRENCY_SHORT}
{ArabicTexts.SECOND_SALARY}: {second_salary:,.0f} {ArabicTexts.CURRENCY_SHORT}
{ArabicTexts.TOTAL_SALARY}: {total:,.0f} {ArabicTexts.CURRENCY_SHORT}
{ArabicTexts.MONTH}: {month} {year}"""
    
    @staticmethod
    def format_latest_salary_info(salary_data):
        """تنسيق معلومات آخر راتب"""
        return f"""{ArabicTexts.LATEST_SALARY}: {salary_data[3]:,.0f} {ArabicTexts.CURRENCY}
{ArabicTexts.FIRST_SALARY}: {salary_data[1]:,.0f} {ArabicTexts.CURRENCY_SHORT}
{ArabicTexts.SECOND_SALARY}: {salary_data[2]:,.0f} {ArabicTexts.CURRENCY_SHORT}
{ArabicTexts.MONTH}: {salary_data[4]}/{salary_data[5]}"""
