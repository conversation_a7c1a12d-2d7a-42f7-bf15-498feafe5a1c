# دليل المستخدم - تطبيق مصروف

## نظرة عامة
تطبيق مصروف هو برنامج مكتبي لإدارة الرواتب والمصروفات والديون الشخصية باللغة العربية.

## الميزات الحالية

### 1. إدخال الرواتب
- إدخال الراتب الأول والثاني
- تحديد الشهر والسنة
- إضافة ملاحظات
- حفظ تلقائي في قاعدة البيانات

### 2. لوحة المعلومات
- عرض آخر راتب مُسجل
- إحصائيات سريعة
- واجهة سهلة الاستخدام

## كيفية الاستخدام

### تشغيل التطبيق
1. تأكد من تثبيت Python 3.11+
2. قم بتشغيل `python main.py` أو انقر مرتين على `run.bat`

### إدخال راتب جديد
1. اضغط على "إدخال الراتب" من القائمة الجانبية
2. أدخل مبلغ الراتب الأول (مطلوب)
3. أدخل مبلغ الراتب الثاني (اختياري)
4. اختر الشهر والسنة
5. أضف ملاحظات إذا أردت
6. اضغط "حفظ الراتب"

### نصائح مهمة
- يمكنك استخدام الفواصل في الأرقام (مثل: 1,000,000)
- الراتب الثاني اختياري ويمكن تركه فارغاً
- يتم حفظ البيانات تلقائياً في ملف `masroof.db`

## الميزات القادمة
- إدارة المصروفات اليومية
- تتبع الديون والالتزامات
- تقارير مالية مفصلة
- تحليل الإنفاق بالذكاء الصناعي
- رسوم بيانية وإحصائيات

## المتطلبات التقنية
- Python 3.11 أو أحدث
- CustomTkinter
- SQLite (مدمج مع Python)
- pandas, numpy, scikit-learn
- matplotlib, plotly

## الدعم الفني
في حالة وجود مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. تحقق من وجود ملف `masroof.db` في نفس مجلد التطبيق
3. تأكد من صحة إدخال البيانات

## حقوق الطبع والنشر
هذا التطبيق مطور للاستخدام الشخصي ومفتوح المصدر.
