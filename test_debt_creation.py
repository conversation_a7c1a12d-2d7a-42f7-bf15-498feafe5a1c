#!/usr/bin/env python3
"""
اختبار إنشاء الديون
Test Debt Creation
"""

from app import app, db, Debt
from datetime import datetime

def test_debt_creation():
    """اختبار إنشاء دين جديد"""
    print("🧪 اختبار إنشاء دين جديد...")
    
    with app.app_context():
        try:
            # إنشاء دين تجريبي
            test_debt = Debt(
                creditor_name="اختبار - بنك الرافدين",
                debt_type="سيارة",
                total_amount=30000000,
                monthly_payment=1500000,
                remaining_amount=30000000,
                installments_count=20,
                monthly_due_date=10,
                description="دين تجريبي للاختبار"
            )
            
            print(f"✅ تم إنشاء كائن الدين: {test_debt.creditor_name}")
            
            # إضافة للجلسة
            db.session.add(test_debt)
            print("✅ تم إضافة الدين للجلسة")
            
            # حفظ في قاعدة البيانات
            db.session.commit()
            print("✅ تم حفظ الدين في قاعدة البيانات")
            
            # التحقق من الحفظ
            saved_debt = Debt.query.filter_by(creditor_name="اختبار - بنك الرافدين").first()
            if saved_debt:
                print(f"✅ تم العثور على الدين المحفوظ: ID={saved_debt.id}")
                print(f"   الدائن: {saved_debt.creditor_name}")
                print(f"   النوع: {saved_debt.debt_type}")
                print(f"   المبلغ: {saved_debt.total_amount:,.0f} د.ع")
                
                # حذف الدين التجريبي
                db.session.delete(saved_debt)
                db.session.commit()
                print("✅ تم حذف الدين التجريبي")
                
                return True
            else:
                print("❌ لم يتم العثور على الدين المحفوظ")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في اختبار إنشاء الدين: {e}")
            db.session.rollback()
            return False

def check_database_structure():
    """فحص هيكل قاعدة البيانات"""
    print("🔍 فحص هيكل قاعدة البيانات...")

    with app.app_context():
        try:
            # فحص جدول الديون
            from sqlalchemy import text
            result = db.session.execute(text("PRAGMA table_info(debt)"))
            columns = result.fetchall()

            print("📋 أعمدة جدول الديون:")
            for column in columns:
                print(f"   - {column[1]} ({column[2]})")

            # عد الديون الموجودة
            debt_count = Debt.query.count()
            print(f"📊 عدد الديون الموجودة: {debt_count}")

            # عرض الديون الموجودة
            if debt_count > 0:
                debts = Debt.query.all()
                print("📋 الديون الموجودة:")
                for debt in debts:
                    print(f"   - {debt.creditor_name} ({debt.debt_type}) - {debt.total_amount:,.0f} د.ع")

            return True

        except Exception as e:
            print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
            return False

def test_form_validation():
    """اختبار التحقق من صحة النموذج"""
    print("📝 اختبار التحقق من صحة النموذج...")

    from app import DebtForm

    with app.app_context():
        with app.test_request_context():
            try:
                # إنشاء نموذج ببيانات صحيحة
                form_data = {
                    'creditor_name': 'بنك بغداد',
                    'debt_type': 'سيارة',
                    'total_amount': '25000000',
                    'monthly_payment': '1250000',
                    'installments_count': '20',
                    'monthly_due_date': '15',
                    'description': 'قرض سيارة',
                    'csrf_token': 'test_token'
                }

                form = DebtForm(data=form_data)

                if form.validate():
                    print("✅ النموذج صحيح")
                    print(f"   الدائن: {form.creditor_name.data}")
                    print(f"   النوع: {form.debt_type.data}")
                    print(f"   المبلغ: {form.total_amount.data}")
                    return True
                else:
                    print("❌ النموذج غير صحيح")
                    print("الأخطاء:")
                    for field, errors in form.errors.items():
                        for error in errors:
                            print(f"   - {field}: {error}")
                    return False

            except Exception as e:
                print(f"❌ خطأ في اختبار النموذج: {e}")
                return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار نظام إنشاء الديون")
    print("=" * 60)
    print()

    # فحص هيكل قاعدة البيانات
    db_ok = check_database_structure()
    print()

    # اختبار إنشاء دين مباشرة (تجاهل مشكلة CSRF في الاختبار)
    if db_ok:
        creation_ok = test_debt_creation()
        print()

        if creation_ok:
            print("🎉 قاعدة البيانات تعمل بشكل صحيح!")
            print("💡 المشكلة قد تكون في النموذج أو CSRF token")
            print("🔧 تحقق من:")
            print("   1. تعبئة جميع الحقول المطلوبة")
            print("   2. اختيار نوع الدين من القائمة")
            print("   3. إدخال مبلغ صحيح")
        else:
            print("❌ فشل اختبار إنشاء الدين")
    else:
        print("❌ مشكلة في قاعدة البيانات")

    print("=" * 60)

if __name__ == "__main__":
    main()
