{% extends "base.html" %}

{% block title %}تعديل الراتب - نظام إدارة الرواتب{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-edit me-3"></i>
            تعديل راتب {{ salary.month }}/{{ salary.year }}
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    نموذج تعديل الراتب
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="editSalaryForm">
                    <div class="row">
                        <!-- الراتب الأول -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-money-bill text-success me-1"></i>
                                الراتب الأول (دينار عراقي) *
                            </label>
                            <input type="number" name="first_salary" class="form-control form-control-lg" 
                                   placeholder="أدخل مبلغ الراتب الأول" required min="0" step="1000"
                                   value="{{ salary.first_salary|int }}">
                            <div class="form-text">الراتب الأساسي أو المصدر الرئيسي للدخل</div>
                        </div>
                        
                        <!-- الراتب الثاني -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-coins text-info me-1"></i>
                                الراتب الثاني (دينار عراقي)
                            </label>
                            <input type="number" name="second_salary" class="form-control form-control-lg" 
                                   placeholder="أدخل مبلغ الراتب الثاني (اختياري)" min="0" step="1000"
                                   value="{{ salary.second_salary|int }}">
                            <div class="form-text">راتب إضافي أو مصدر دخل ثانوي (اختياري)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الشهر -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar text-primary me-1"></i>
                                الشهر *
                            </label>
                            <select name="month" class="form-select form-select-lg" required>
                                <option value="">اختر الشهر</option>
                                <option value="1" {% if salary.month == 1 %}selected{% endif %}>يناير</option>
                                <option value="2" {% if salary.month == 2 %}selected{% endif %}>فبراير</option>
                                <option value="3" {% if salary.month == 3 %}selected{% endif %}>مارس</option>
                                <option value="4" {% if salary.month == 4 %}selected{% endif %}>أبريل</option>
                                <option value="5" {% if salary.month == 5 %}selected{% endif %}>مايو</option>
                                <option value="6" {% if salary.month == 6 %}selected{% endif %}>يونيو</option>
                                <option value="7" {% if salary.month == 7 %}selected{% endif %}>يوليو</option>
                                <option value="8" {% if salary.month == 8 %}selected{% endif %}>أغسطس</option>
                                <option value="9" {% if salary.month == 9 %}selected{% endif %}>سبتمبر</option>
                                <option value="10" {% if salary.month == 10 %}selected{% endif %}>أكتوبر</option>
                                <option value="11" {% if salary.month == 11 %}selected{% endif %}>نوفمبر</option>
                                <option value="12" {% if salary.month == 12 %}selected{% endif %}>ديسمبر</option>
                            </select>
                        </div>
                        
                        <!-- السنة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-alt text-warning me-1"></i>
                                السنة *
                            </label>
                            <input type="number" name="year" class="form-control form-control-lg" 
                                   placeholder="أدخل السنة" required min="2020" max="2030"
                                   value="{{ salary.year }}">
                        </div>
                    </div>
                    
                    <!-- الملاحظات -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-sticky-note text-secondary me-1"></i>
                            ملاحظات إضافية
                        </label>
                        <textarea name="notes" class="form-control" rows="4" 
                                  placeholder="أضف أي ملاحظات مهمة مثل المكافآت، الخصومات، أو تفاصيل أخرى...">{{ salary.notes or '' }}</textarea>
                        <div class="form-text">يمكنك إضافة تفاصيل إضافية مثل المكافآت أو الخصومات</div>
                    </div>
                    
                    <!-- عرض المجموع -->
                    <div class="mb-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="text-muted mb-2">المجموع الإجمالي</h6>
                                <h3 class="text-primary mb-0" id="totalAmount">{{ salary.total_salary|currency }}</h3>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات إضافية -->
                    <div class="mb-4">
                        <div class="card border-info">
                            <div class="card-body">
                                <h6 class="card-title text-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات الراتب
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">تاريخ الإضافة:</small>
                                        <p class="mb-1">{{ salary.date_added.strftime('%Y-%m-%d %H:%M') }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">المجموع الحالي:</small>
                                        <p class="mb-1 text-primary fw-bold">{{ salary.total_salary|currency }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار العمليات -->
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <button type="submit" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="{{ url_for('salaries') }}" class="btn btn-outline-secondary btn-lg w-100">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button type="button" class="btn btn-outline-danger btn-lg w-100" 
                                    onclick="confirmDelete({{ salary.id }}, '{{ salary.month }}/{{ salary.year }}')">
                                <i class="fas fa-trash me-2"></i>
                                حذف الراتب
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح للتعديل
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-exclamation-triangle text-warning me-2 mt-1"></i>
                            <div>
                                <strong>تحقق من البيانات</strong>
                                <p class="text-muted small mb-0">تأكد من صحة المبالغ والتواريخ قبل الحفظ</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calendar-check text-info me-2 mt-1"></i>
                            <div>
                                <strong>تجنب التكرار</strong>
                                <p class="text-muted small mb-0">تأكد من عدم وجود راتب آخر لنفس الشهر والسنة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-save text-success me-2 mt-1"></i>
                            <div>
                                <strong>احفظ التغييرات</strong>
                                <p class="text-muted small mb-0">لا تنس حفظ التعديلات بعد الانتهاء</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // حساب المجموع تلقائياً
        const firstSalaryInput = document.querySelector('input[name="first_salary"]');
        const secondSalaryInput = document.querySelector('input[name="second_salary"]');
        const totalAmountDisplay = document.getElementById('totalAmount');
        
        function updateTotal() {
            const first = parseFloat(firstSalaryInput.value) || 0;
            const second = parseFloat(secondSalaryInput.value) || 0;
            const total = first + second;
            
            totalAmountDisplay.textContent = total.toLocaleString('ar-IQ') + ' د.ع';
            
            // تغيير لون المجموع حسب القيمة
            if (total > 0) {
                totalAmountDisplay.className = 'text-success mb-0';
            } else {
                totalAmountDisplay.className = 'text-muted mb-0';
            }
        }
        
        // ربط الأحداث
        firstSalaryInput.addEventListener('input', updateTotal);
        secondSalaryInput.addEventListener('input', updateTotal);
        
        // تحسين النموذج
        const form = document.getElementById('editSalaryForm');
        form.addEventListener('submit', function(e) {
            const firstSalary = parseFloat(firstSalaryInput.value) || 0;
            
            if (firstSalary <= 0) {
                e.preventDefault();
                alert('يرجى إدخال مبلغ الراتب الأول');
                firstSalaryInput.focus();
                return false;
            }
            
            // تأكيد الحفظ
            if (!confirm('هل أنت متأكد من حفظ التعديلات؟')) {
                e.preventDefault();
                return false;
            }
        });
        
        // تنسيق المدخلات الرقمية
        [firstSalaryInput, secondSalaryInput].forEach(input => {
            input.addEventListener('input', function() {
                // إزالة الأحرف غير الرقمية
                this.value = this.value.replace(/[^0-9]/g, '');
            });
        });
    });
    
    function confirmDelete(salaryId, monthYear) {
        if (confirm(`هل أنت متأكد من حذف راتب ${monthYear}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // إنشاء نموذج مخفي لإرسال طلب الحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/delete_salary/${salaryId}`;
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}
