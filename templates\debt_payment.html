{% extends "base.html" %}

{% block title %}تسجيل دفعة دين - مصروف{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <h2 class="text-white mb-4">
            <i class="fas fa-money-bill-wave me-2"></i>
            تسجيل دفعة دين
        </h2>
    </div>
</div>

<div class="row">
    <!-- نموذج تسجيل الدفعة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>
                    تسجيل دفعة جديدة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.debt_id.label(class="form-label") }}
                        {{ form.debt_id(class="form-select") }}
                        {% if form.debt_id.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.debt_id.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.amount.label(class="form-label") }}
                        {{ form.amount(class="form-control", placeholder="أدخل مبلغ الدفعة") }}
                        {% if form.amount.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.amount.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.payment_date.label(class="form-label") }}
                        {{ form.payment_date(class="form-control", type="date", value=today()) }}
                        {% if form.payment_date.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.payment_date.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.payment_type.label(class="form-label") }}
                        {{ form.payment_type(class="form-select") }}
                        {% if form.payment_type.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.payment_type.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.receipt_number.label(class="form-label") }}
                        {{ form.receipt_number(class="form-control", placeholder="رقم الإيصال أو المرجع") }}
                        <small class="text-muted">اختياري</small>
                        {% if form.receipt_number.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.receipt_number.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows="3", placeholder="ملاحظات إضافية...") }}
                        {% if form.notes.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.notes.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>
                            تسجيل الدفعة
                        </button>
                        <a href="{{ url_for('debts') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للديون
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- آخر الدفعات -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2 text-primary"></i>
                    آخر الدفعات المسجلة
                </h5>
            </div>
            <div class="card-body">
                {% if payments %}
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>الدين</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>
                                        <small>
                                            <strong>{{ payment.debt.creditor_name }}</strong><br>
                                            <span class="text-muted">{{ payment.debt.debt_type }}</span>
                                        </small>
                                    </td>
                                    <td class="text-success fw-bold">
                                        {{ "{:,.0f}".format(payment.amount) }} د.ع
                                    </td>
                                    <td class="text-muted">
                                        {{ payment.payment_date.strftime('%Y-%m-%d') }}
                                    </td>
                                    <td>
                                        {% set payment_types = {
                                            'installment': ('قسط شهري', 'primary'),
                                            'extra': ('دفعة إضافية', 'success'),
                                            'early': ('سداد مبكر', 'warning'),
                                            'penalty': ('غرامة تأخير', 'danger')
                                        } %}
                                        {% set type_info = payment_types.get(payment.payment_type, ('أخرى', 'secondary')) %}
                                        <span class="badge bg-{{ type_info[1] }}">{{ type_info[0] }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-receipt fa-2x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد دفعات مسجلة بعد</h6>
                        <p class="text-muted">ابدأ بتسجيل دفعتك الأولى</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نصائح لتسجيل الدفعات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح لتسجيل الدفعات
                </h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-receipt text-success me-2 mt-1"></i>
                            <div>
                                <strong>احتفظ بالإيصالات</strong>
                                <p class="text-muted small mb-0">سجل رقم الإيصال لكل دفعة للمراجعة لاحقاً</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calendar text-info me-2 mt-1"></i>
                            <div>
                                <strong>سجل فوراً</strong>
                                <p class="text-muted small mb-0">سجل الدفعة فور تسديدها لتجنب النسيان</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-tags text-warning me-2 mt-1"></i>
                            <div>
                                <strong>حدد النوع بدقة</strong>
                                <p class="text-muted small mb-0">اختر نوع الدفعة المناسب لتتبع أفضل</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-sticky-note text-primary me-2 mt-1"></i>
                            <div>
                                <strong>أضف ملاحظات</strong>
                                <p class="text-muted small mb-0">اكتب أي تفاصيل مهمة عن الدفعة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث معلومات الدين عند اختياره
    document.addEventListener('DOMContentLoaded', function() {
        const debtSelect = document.getElementById('debt_id');
        const amountInput = document.getElementById('amount');
        
        if (debtSelect && amountInput) {
            debtSelect.addEventListener('change', function() {
                // يمكن إضافة AJAX هنا لجلب معلومات الدين المحدد
                // وتعبئة القسط الشهري تلقائياً
            });
        }
        
        // تعيين التاريخ الحالي إذا لم يكن محدد
        const dateInput = document.getElementById('payment_date');
        if (dateInput && !dateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            dateInput.value = today;
        }
    });
</script>
{% endblock %}
