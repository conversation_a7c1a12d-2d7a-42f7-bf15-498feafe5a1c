#!/usr/bin/env python3
"""
نظام إدارة الرواتب - مشروع بسيط ومركز
Salary Management System - Simple and Focused
"""

from flask import Flask, render_template, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'salary-management-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///salaries.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# نموذج الراتب
class Salary(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    first_salary = db.Column(db.Float, nullable=False)
    second_salary = db.Column(db.Float, default=0)
    total_salary = db.Column(db.Float, nullable=False)
    month = db.Column(db.Integer, nullable=False)
    year = db.Column(db.Integer, nullable=False)
    notes = db.Column(db.Text)
    date_added = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Salary {self.month}/{self.year}: {self.total_salary}>'

# نموذج الدين
class Debt(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    creditor_name = db.Column(db.String(100), nullable=False)
    debt_type = db.Column(db.String(50), nullable=False)
    total_amount = db.Column(db.Float, nullable=False)
    remaining_amount = db.Column(db.Float, nullable=False)
    monthly_payment = db.Column(db.Float)
    installments_count = db.Column(db.Integer)
    paid_installments = db.Column(db.Integer, default=0)
    monthly_due_date = db.Column(db.Integer)
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default='active')
    date_added = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Debt {self.creditor_name}: {self.remaining_amount}>'

    @property
    def progress_percentage(self):
        """حساب نسبة السداد"""
        if self.total_amount > 0:
            paid = self.total_amount - self.remaining_amount
            return (paid / self.total_amount) * 100
        return 0

    @property
    def is_paid(self):
        """التحقق من السداد الكامل"""
        return self.remaining_amount <= 0

# نموذج دفعة الدين
class DebtPayment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    debt_id = db.Column(db.Integer, db.ForeignKey('debt.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_date = db.Column(db.Date, nullable=False)
    receipt_number = db.Column(db.String(50))
    notes = db.Column(db.Text)
    date_added = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقة مع الدين
    debt = db.relationship('Debt', backref=db.backref('payments', lazy=True, cascade='all, delete-orphan'))

    def __repr__(self):
        return f'<DebtPayment {self.amount} for Debt {self.debt_id}>'

# الصفحة الرئيسية
@app.route('/')
def index():
    """الصفحة الرئيسية - عرض ملخص الرواتب"""
    # إحصائيات عامة
    total_salaries = Salary.query.count()
    total_amount = db.session.query(db.func.sum(Salary.total_salary)).scalar() or 0
    avg_salary = total_amount / total_salaries if total_salaries > 0 else 0
    
    # آخر 5 رواتب
    recent_salaries = Salary.query.order_by(Salary.date_added.desc()).limit(5).all()
    
    # الراتب الحالي (هذا الشهر)
    current_month = datetime.now().month
    current_year = datetime.now().year
    current_salary = Salary.query.filter_by(month=current_month, year=current_year).first()
    
    return render_template('index.html',
                         total_salaries=total_salaries,
                         total_amount=total_amount,
                         avg_salary=avg_salary,
                         recent_salaries=recent_salaries,
                         current_salary=current_salary)

# عرض جميع الرواتب
@app.route('/salaries')
def salaries():
    """عرض جميع الرواتب"""
    all_salaries = Salary.query.order_by(Salary.year.desc(), Salary.month.desc()).all()
    return render_template('salaries.html', salaries=all_salaries)

# إضافة راتب جديد
@app.route('/add_salary', methods=['GET', 'POST'])
def add_salary():
    """إضافة راتب جديد"""
    if request.method == 'POST':
        try:
            # الحصول على البيانات
            first_salary = float(request.form.get('first_salary', 0))
            second_salary = float(request.form.get('second_salary', 0))
            month = int(request.form.get('month'))
            year = int(request.form.get('year'))
            notes = request.form.get('notes', '').strip()
            
            # التحقق من البيانات
            if first_salary <= 0:
                flash('يرجى إدخال الراتب الأول بشكل صحيح', 'error')
                return render_template('add_salary.html')
            
            # التحقق من عدم وجود راتب لنفس الشهر والسنة
            existing = Salary.query.filter_by(month=month, year=year).first()
            if existing:
                flash(f'يوجد راتب مسجل مسبقاً لشهر {month}/{year}', 'warning')
                return render_template('add_salary.html')
            
            # حساب المجموع
            total = first_salary + second_salary
            
            # إنشاء راتب جديد
            new_salary = Salary(
                first_salary=first_salary,
                second_salary=second_salary,
                total_salary=total,
                month=month,
                year=year,
                notes=notes if notes else None
            )
            
            # حفظ في قاعدة البيانات
            db.session.add(new_salary)
            db.session.commit()
            
            flash(f'تم حفظ الراتب بنجاح! المجموع: {total:,.0f} دينار عراقي', 'success')
            return redirect(url_for('salaries'))
            
        except ValueError:
            flash('خطأ في البيانات المدخلة', 'error')
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
            db.session.rollback()
    
    return render_template('add_salary.html')

# تعديل راتب
@app.route('/edit_salary/<int:salary_id>', methods=['GET', 'POST'])
def edit_salary(salary_id):
    """تعديل راتب موجود"""
    salary = Salary.query.get_or_404(salary_id)
    
    if request.method == 'POST':
        try:
            # الحصول على البيانات
            first_salary = float(request.form.get('first_salary', 0))
            second_salary = float(request.form.get('second_salary', 0))
            month = int(request.form.get('month'))
            year = int(request.form.get('year'))
            notes = request.form.get('notes', '').strip()
            
            # التحقق من البيانات
            if first_salary <= 0:
                flash('يرجى إدخال الراتب الأول بشكل صحيح', 'error')
                return render_template('edit_salary.html', salary=salary)
            
            # التحقق من عدم وجود راتب آخر لنفس الشهر والسنة
            existing = Salary.query.filter_by(month=month, year=year).filter(Salary.id != salary_id).first()
            if existing:
                flash(f'يوجد راتب آخر مسجل لشهر {month}/{year}', 'warning')
                return render_template('edit_salary.html', salary=salary)
            
            # تحديث البيانات
            salary.first_salary = first_salary
            salary.second_salary = second_salary
            salary.total_salary = first_salary + second_salary
            salary.month = month
            salary.year = year
            salary.notes = notes if notes else None
            
            # حفظ التغييرات
            db.session.commit()
            
            flash(f'تم تحديث الراتب بنجاح! المجموع: {salary.total_salary:,.0f} دينار عراقي', 'success')
            return redirect(url_for('salaries'))
            
        except ValueError:
            flash('خطأ في البيانات المدخلة', 'error')
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
            db.session.rollback()
    
    return render_template('edit_salary.html', salary=salary)

# حذف راتب
@app.route('/delete_salary/<int:salary_id>', methods=['POST'])
def delete_salary(salary_id):
    """حذف راتب"""
    try:
        salary = Salary.query.get_or_404(salary_id)
        month_year = f"{salary.month}/{salary.year}"
        
        db.session.delete(salary)
        db.session.commit()
        
        flash(f'تم حذف راتب {month_year} بنجاح!', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'error')
        db.session.rollback()
    
    return redirect(url_for('salaries'))

# ===== إدارة الديون =====

# عرض جميع الديون
@app.route('/debts')
def debts():
    """عرض جميع الديون"""
    all_debts = Debt.query.filter_by(status='active').order_by(Debt.date_added.desc()).all()

    # إحصائيات الديون
    total_debts = len(all_debts)
    total_debt_amount = sum(debt.remaining_amount for debt in all_debts)
    total_original_amount = sum(debt.total_amount for debt in all_debts)
    total_paid = total_original_amount - total_debt_amount

    return render_template('debts.html',
                         debts=all_debts,
                         total_debts=total_debts,
                         total_debt_amount=total_debt_amount,
                         total_original_amount=total_original_amount,
                         total_paid=total_paid)

# إضافة دين جديد
@app.route('/add_debt', methods=['GET', 'POST'])
def add_debt():
    """إضافة دين جديد"""
    if request.method == 'POST':
        try:
            # الحصول على البيانات
            creditor_name = request.form.get('creditor_name', '').strip()
            debt_type = request.form.get('debt_type', '').strip()
            total_amount = float(request.form.get('total_amount', 0))
            monthly_payment = request.form.get('monthly_payment', '')
            installments_count = request.form.get('installments_count', '')
            monthly_due_date = request.form.get('monthly_due_date', '')
            start_date_str = request.form.get('start_date', '')
            end_date_str = request.form.get('end_date', '')
            description = request.form.get('description', '').strip()

            # التحقق من البيانات
            if not creditor_name:
                flash('يرجى إدخال اسم الدائن/الجهة', 'error')
                return render_template('add_debt.html')

            if not debt_type:
                flash('يرجى اختيار نوع الدين', 'error')
                return render_template('add_debt.html')

            if total_amount <= 0:
                flash('يرجى إدخال مبلغ إجمالي صحيح', 'error')
                return render_template('add_debt.html')

            # معالجة التواريخ
            start_date = None
            end_date = None
            if start_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            if end_date_str:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

            # إنشاء دين جديد
            new_debt = Debt(
                creditor_name=creditor_name,
                debt_type=debt_type,
                total_amount=total_amount,
                remaining_amount=total_amount,
                monthly_payment=float(monthly_payment) if monthly_payment else None,
                installments_count=int(installments_count) if installments_count else None,
                monthly_due_date=int(monthly_due_date) if monthly_due_date else None,
                start_date=start_date,
                end_date=end_date,
                description=description if description else None
            )

            # حفظ في قاعدة البيانات
            db.session.add(new_debt)
            db.session.commit()

            flash(f'تم حفظ الدين بنجاح! الدائن: {creditor_name} - المبلغ: {total_amount:,.0f} دينار عراقي', 'success')
            return redirect(url_for('debts'))

        except ValueError:
            flash('خطأ في البيانات المدخلة', 'error')
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
            db.session.rollback()

    return render_template('add_debt.html')

# تعديل دين
@app.route('/edit_debt/<int:debt_id>', methods=['GET', 'POST'])
def edit_debt(debt_id):
    """تعديل دين موجود"""
    debt = Debt.query.get_or_404(debt_id)

    if request.method == 'POST':
        try:
            # الحصول على البيانات
            creditor_name = request.form.get('creditor_name', '').strip()
            debt_type = request.form.get('debt_type', '').strip()
            total_amount = float(request.form.get('total_amount', 0))
            monthly_payment = request.form.get('monthly_payment', '')
            installments_count = request.form.get('installments_count', '')
            monthly_due_date = request.form.get('monthly_due_date', '')
            start_date_str = request.form.get('start_date', '')
            end_date_str = request.form.get('end_date', '')
            description = request.form.get('description', '').strip()

            # التحقق من البيانات
            if not creditor_name:
                flash('يرجى إدخال اسم الدائن/الجهة', 'error')
                return render_template('edit_debt.html', debt=debt)

            if not debt_type:
                flash('يرجى اختيار نوع الدين', 'error')
                return render_template('edit_debt.html', debt=debt)

            if total_amount <= 0:
                flash('يرجى إدخال مبلغ إجمالي صحيح', 'error')
                return render_template('edit_debt.html', debt=debt)

            # معالجة التواريخ
            start_date = None
            end_date = None
            if start_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            if end_date_str:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

            # حساب المبلغ المتبقي الجديد
            paid_amount = debt.total_amount - debt.remaining_amount
            new_remaining = max(0, total_amount - paid_amount)

            # تحديث البيانات
            debt.creditor_name = creditor_name
            debt.debt_type = debt_type
            debt.total_amount = total_amount
            debt.remaining_amount = new_remaining
            debt.monthly_payment = float(monthly_payment) if monthly_payment else None
            debt.installments_count = int(installments_count) if installments_count else None
            debt.monthly_due_date = int(monthly_due_date) if monthly_due_date else None
            debt.start_date = start_date
            debt.end_date = end_date
            debt.description = description if description else None

            # تحديث الحالة
            if debt.remaining_amount <= 0:
                debt.status = 'paid'

            # حفظ التغييرات
            db.session.commit()

            flash(f'تم تحديث الدين بنجاح! المبلغ المتبقي: {debt.remaining_amount:,.0f} دينار عراقي', 'success')
            return redirect(url_for('debts'))

        except ValueError:
            flash('خطأ في البيانات المدخلة', 'error')
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
            db.session.rollback()

    return render_template('edit_debt.html', debt=debt)

# حذف دين
@app.route('/delete_debt/<int:debt_id>', methods=['POST'])
def delete_debt(debt_id):
    """حذف دين"""
    try:
        debt = Debt.query.get_or_404(debt_id)
        creditor_name = debt.creditor_name

        db.session.delete(debt)
        db.session.commit()

        flash(f'تم حذف دين "{creditor_name}" بنجاح!', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'error')
        db.session.rollback()

    return redirect(url_for('debts'))

# ===== إدارة دفعات الديون =====

# عرض تفاصيل دين مع دفعاته
@app.route('/debt_details/<int:debt_id>')
def debt_details(debt_id):
    """عرض تفاصيل دين مع جميع دفعاته"""
    debt = Debt.query.get_or_404(debt_id)
    payments = DebtPayment.query.filter_by(debt_id=debt_id).order_by(DebtPayment.payment_date.desc()).all()

    # إحصائيات الدفعات
    total_payments = len(payments)
    total_paid_amount = sum(payment.amount for payment in payments)

    return render_template('debt_details.html',
                         debt=debt,
                         payments=payments,
                         total_payments=total_payments,
                         total_paid_amount=total_paid_amount)

# إضافة دفعة جديدة
@app.route('/add_payment/<int:debt_id>', methods=['GET', 'POST'])
def add_payment(debt_id):
    """إضافة دفعة جديدة لدين"""
    debt = Debt.query.get_or_404(debt_id)

    if request.method == 'POST':
        try:
            # الحصول على البيانات
            amount = float(request.form.get('amount', 0))
            payment_date_str = request.form.get('payment_date', '')
            receipt_number = request.form.get('receipt_number', '').strip()
            notes = request.form.get('notes', '').strip()

            # التحقق من البيانات
            if amount <= 0:
                flash('يرجى إدخال مبلغ الدفعة بشكل صحيح', 'error')
                return render_template('add_payment.html', debt=debt)

            if amount > debt.remaining_amount:
                flash(f'مبلغ الدفعة ({amount:,.0f} د.ع) أكبر من المبلغ المتبقي ({debt.remaining_amount:,.0f} د.ع)', 'error')
                return render_template('add_payment.html', debt=debt)

            if not payment_date_str:
                flash('يرجى اختيار تاريخ الدفعة', 'error')
                return render_template('add_payment.html', debt=debt)

            payment_date = datetime.strptime(payment_date_str, '%Y-%m-%d').date()

            # إنشاء دفعة جديدة
            new_payment = DebtPayment(
                debt_id=debt_id,
                amount=amount,
                payment_date=payment_date,
                receipt_number=receipt_number if receipt_number else None,
                notes=notes if notes else None
            )

            # تحديث المبلغ المتبقي في الدين
            debt.remaining_amount = max(0, debt.remaining_amount - amount)
            debt.paid_installments += 1

            # تحديث حالة الدين إذا تم السداد بالكامل
            if debt.remaining_amount <= 0:
                debt.status = 'paid'

            # حفظ في قاعدة البيانات
            db.session.add(new_payment)
            db.session.commit()

            flash(f'تم تسجيل الدفعة بنجاح! المبلغ: {amount:,.0f} د.ع - المتبقي: {debt.remaining_amount:,.0f} د.ع', 'success')
            return redirect(url_for('debt_details', debt_id=debt_id))

        except ValueError:
            flash('خطأ في البيانات المدخلة', 'error')
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
            db.session.rollback()

    return render_template('add_payment.html', debt=debt)

# تعديل دفعة
@app.route('/edit_payment/<int:payment_id>', methods=['GET', 'POST'])
def edit_payment(payment_id):
    """تعديل دفعة موجودة"""
    payment = DebtPayment.query.get_or_404(payment_id)
    debt = payment.debt

    if request.method == 'POST':
        try:
            # الحصول على البيانات
            new_amount = float(request.form.get('amount', 0))
            payment_date_str = request.form.get('payment_date', '')
            receipt_number = request.form.get('receipt_number', '').strip()
            notes = request.form.get('notes', '').strip()

            # التحقق من البيانات
            if new_amount <= 0:
                flash('يرجى إدخال مبلغ الدفعة بشكل صحيح', 'error')
                return render_template('edit_payment.html', payment=payment, debt=debt)

            if not payment_date_str:
                flash('يرجى اختيار تاريخ الدفعة', 'error')
                return render_template('edit_payment.html', payment=payment, debt=debt)

            payment_date = datetime.strptime(payment_date_str, '%Y-%m-%d').date()

            # حساب الفرق في المبلغ
            amount_difference = new_amount - payment.amount

            # التحقق من أن المبلغ الجديد لا يتجاوز المتاح
            if amount_difference > debt.remaining_amount:
                available = debt.remaining_amount + payment.amount
                flash(f'المبلغ المتاح للدفع هو {available:,.0f} د.ع فقط', 'error')
                return render_template('edit_payment.html', payment=payment, debt=debt)

            # تحديث بيانات الدفعة
            payment.amount = new_amount
            payment.payment_date = payment_date
            payment.receipt_number = receipt_number if receipt_number else None
            payment.notes = notes if notes else None

            # تحديث المبلغ المتبقي في الدين
            debt.remaining_amount = max(0, debt.remaining_amount - amount_difference)

            # تحديث حالة الدين
            if debt.remaining_amount <= 0:
                debt.status = 'paid'
            else:
                debt.status = 'active'

            # حفظ التغييرات
            db.session.commit()

            flash(f'تم تحديث الدفعة بنجاح! المبلغ الجديد: {new_amount:,.0f} د.ع', 'success')
            return redirect(url_for('debt_details', debt_id=debt.id))

        except ValueError:
            flash('خطأ في البيانات المدخلة', 'error')
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
            db.session.rollback()

    return render_template('edit_payment.html', payment=payment, debt=debt)

# حذف دفعة
@app.route('/delete_payment/<int:payment_id>', methods=['POST'])
def delete_payment(payment_id):
    """حذف دفعة"""
    try:
        payment = DebtPayment.query.get_or_404(payment_id)
        debt = payment.debt
        amount = payment.amount
        debt_id = debt.id

        # إعادة المبلغ للدين
        debt.remaining_amount += amount
        debt.paid_installments = max(0, debt.paid_installments - 1)
        debt.status = 'active'  # إعادة تفعيل الدين

        # حذف الدفعة
        db.session.delete(payment)
        db.session.commit()

        flash(f'تم حذف الدفعة بنجاح! تم إعادة {amount:,.0f} د.ع للمبلغ المتبقي', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'error')
        db.session.rollback()

    return redirect(url_for('debt_details', debt_id=debt_id))

# دالة مساعدة لتنسيق العملة
@app.template_filter('currency')
def currency_filter(amount):
    """تنسيق العملة"""
    return f"{amount:,.0f} د.ع"

# إنشاء الجداول
with app.app_context():
    db.create_all()

if __name__ == '__main__':
    print("🚀 نظام إدارة الرواتب")
    print("📊 الصفحة الرئيسية: http://localhost:5000")
    print("💰 إدارة الرواتب: http://localhost:5000/salaries")
    print("➕ إضافة راتب: http://localhost:5000/add_salary")
    app.run(debug=True, host='0.0.0.0', port=5000)
