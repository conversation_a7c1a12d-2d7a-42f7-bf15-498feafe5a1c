from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_wtf import FlaskForm
from wtforms import StringField, IntegerField, TextAreaField, SelectField, DecimalField
from wtforms.validators import DataRequired, NumberRange, Optional
from datetime import datetime
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'masroof-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///masroof.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# نماذج قاعدة البيانات
class Salary(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    first_salary = db.Column(db.Float, nullable=False)
    second_salary = db.Column(db.Float, default=0)
    total_salary = db.Column(db.Float, nullable=False)
    month = db.Column(db.Integer, nullable=False)
    year = db.Column(db.Integer, nullable=False)
    notes = db.Column(db.Text)
    date_added = db.Column(db.DateTime, default=datetime.utcnow)

class Expense(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Float, nullable=False)
    category = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    expense_date = db.Column(db.Date, nullable=False)
    date_added = db.Column(db.DateTime, default=datetime.utcnow)

class Debt(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    creditor_name = db.Column(db.String(100), nullable=False)
    debt_type = db.Column(db.String(50), nullable=False)  # نوع الدين (سيارة، منزل، شخصي، إلخ)
    total_amount = db.Column(db.Float, nullable=False)  # المبلغ الإجمالي
    monthly_payment = db.Column(db.Float)  # القسط الشهري
    remaining_amount = db.Column(db.Float, nullable=False)  # المبلغ المتبقي
    installments_count = db.Column(db.Integer)  # عدد الأقساط الإجمالي
    paid_installments = db.Column(db.Integer, default=0)  # عدد الأقساط المدفوعة
    monthly_due_date = db.Column(db.Integer)  # يوم الاستحقاق الشهري (1-31)
    start_date = db.Column(db.Date)  # تاريخ بداية القرض
    end_date = db.Column(db.Date)  # تاريخ انتهاء القرض
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default='active')
    date_added = db.Column(db.DateTime, default=datetime.utcnow)

    # علاقة مع جدول الدفعات
    payments = db.relationship('DebtPayment', backref='debt', lazy=True, cascade='all, delete-orphan')

class DebtPayment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    debt_id = db.Column(db.Integer, db.ForeignKey('debt.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_date = db.Column(db.Date, nullable=False)
    payment_type = db.Column(db.String(50), default='installment')  # نوع الدفعة (قسط، دفعة إضافية)
    receipt_number = db.Column(db.String(100))  # رقم الإيصال
    notes = db.Column(db.Text)
    date_added = db.Column(db.DateTime, default=datetime.utcnow)

# نماذج الفورم
class SalaryForm(FlaskForm):
    first_salary = DecimalField('الراتب الأول (دينار عراقي)', validators=[DataRequired()], places=0)
    second_salary = DecimalField('الراتب الثاني (اختياري)', validators=[Optional()], places=0, default=0)
    month = SelectField('الشهر', choices=[
        (1, 'يناير'), (2, 'فبراير'), (3, 'مارس'), (4, 'أبريل'),
        (5, 'مايو'), (6, 'يونيو'), (7, 'يوليو'), (8, 'أغسطس'),
        (9, 'سبتمبر'), (10, 'أكتوبر'), (11, 'نوفمبر'), (12, 'ديسمبر')
    ], coerce=int, validators=[DataRequired()])
    year = IntegerField('السنة', validators=[DataRequired(), NumberRange(min=2020, max=2030)])
    notes = TextAreaField('ملاحظات (اختياري)')

class ExpenseForm(FlaskForm):
    amount = DecimalField('المبلغ (دينار عراقي)', validators=[DataRequired()], places=0)
    category = SelectField('الفئة', choices=[
        ('طعام', 'طعام وشراب'),
        ('مواصلات', 'مواصلات'),
        ('فواتير', 'فواتير'),
        ('ترفيه', 'ترفيه'),
        ('صحة', 'صحة'),
        ('تعليم', 'تعليم'),
        ('ملابس', 'ملابس'),
        ('أخرى', 'أخرى')
    ], validators=[DataRequired()])
    description = StringField('الوصف', validators=[DataRequired()])
    expense_date = StringField('تاريخ المصروف', validators=[DataRequired()])

class DebtForm(FlaskForm):
    creditor_name = StringField('اسم الدائن/الجهة', validators=[DataRequired()])
    debt_type = SelectField('نوع الدين', choices=[
        ('سيارة', 'قرض سيارة'),
        ('منزل', 'قرض منزل/عقار'),
        ('شخصي', 'قرض شخصي'),
        ('بطاقة_ائتمان', 'بطاقة ائتمان'),
        ('تجاري', 'قرض تجاري'),
        ('تعليمي', 'قرض تعليمي'),
        ('طبي', 'قرض طبي'),
        ('أخرى', 'أخرى')
    ], validators=[DataRequired()])
    total_amount = DecimalField('المبلغ الإجمالي (دينار عراقي)', validators=[DataRequired()], places=0)
    monthly_payment = DecimalField('القسط الشهري (دينار عراقي)', validators=[Optional()], places=0)
    installments_count = IntegerField('عدد الأقساط الإجمالي', validators=[Optional()])
    monthly_due_date = IntegerField('يوم الاستحقاق الشهري (1-31)', validators=[Optional(), NumberRange(min=1, max=31)])
    start_date = StringField('تاريخ بداية القرض')
    end_date = StringField('تاريخ انتهاء القرض')
    description = TextAreaField('وصف الدين')

class DebtPaymentForm(FlaskForm):
    debt_id = SelectField('الدين', coerce=int, validators=[DataRequired()])
    amount = DecimalField('مبلغ الدفعة (دينار عراقي)', validators=[DataRequired()], places=0)
    payment_date = StringField('تاريخ الدفعة', validators=[DataRequired()])
    payment_type = SelectField('نوع الدفعة', choices=[
        ('installment', 'قسط شهري'),
        ('extra', 'دفعة إضافية'),
        ('early', 'سداد مبكر'),
        ('penalty', 'غرامة تأخير')
    ], validators=[DataRequired()])
    receipt_number = StringField('رقم الإيصال')
    notes = TextAreaField('ملاحظات')

# الصفحات
@app.route('/')
def index():
    """الصفحة الرئيسية - لوحة المعلومات"""
    # إحصائيات سريعة
    latest_salary = Salary.query.order_by(Salary.date_added.desc()).first()
    total_expenses_month = db.session.query(db.func.sum(Expense.amount)).filter(
        db.extract('month', Expense.expense_date) == datetime.now().month,
        db.extract('year', Expense.expense_date) == datetime.now().year
    ).scalar() or 0
    
    active_debts = Debt.query.filter_by(status='active').count()
    total_debt_amount = db.session.query(db.func.sum(Debt.remaining_amount)).filter_by(status='active').scalar() or 0
    
    # آخر المعاملات
    recent_expenses = Expense.query.order_by(Expense.date_added.desc()).limit(5).all()
    
    return render_template('index.html', 
                         latest_salary=latest_salary,
                         total_expenses_month=total_expenses_month,
                         active_debts=active_debts,
                         total_debt_amount=total_debt_amount,
                         recent_expenses=recent_expenses)

@app.route('/salary', methods=['GET', 'POST'])
def salary():
    """صفحة إدارة الرواتب"""
    form = SalaryForm()
    
    # تعيين القيم الافتراضية
    if request.method == 'GET':
        form.month.data = datetime.now().month
        form.year.data = datetime.now().year
    
    if form.validate_on_submit():
        total = float(form.first_salary.data) + float(form.second_salary.data or 0)
        
        new_salary = Salary(
            first_salary=float(form.first_salary.data),
            second_salary=float(form.second_salary.data or 0),
            total_salary=total,
            month=form.month.data,
            year=form.year.data,
            notes=form.notes.data
        )
        
        db.session.add(new_salary)
        db.session.commit()
        
        flash(f'تم حفظ الراتب بنجاح! المجموع: {total:,.0f} دينار عراقي', 'success')
        return redirect(url_for('salary'))
    
    # عرض الرواتب المحفوظة
    salaries = Salary.query.order_by(Salary.date_added.desc()).all()
    
    return render_template('salary.html', form=form, salaries=salaries)

@app.route('/expenses', methods=['GET', 'POST'])
def expenses():
    """صفحة إدارة المصروفات"""
    form = ExpenseForm()

    # تعيين التاريخ الحالي كقيمة افتراضية
    if request.method == 'GET':
        form.expense_date.data = datetime.now().strftime('%Y-%m-%d')

    if form.validate_on_submit():
        expense_date = datetime.strptime(form.expense_date.data, '%Y-%m-%d').date()
        
        new_expense = Expense(
            amount=float(form.amount.data),
            category=form.category.data,
            description=form.description.data,
            expense_date=expense_date
        )
        
        db.session.add(new_expense)
        db.session.commit()
        
        flash(f'تم حفظ المصروف بنجاح! المبلغ: {form.amount.data:,.0f} دينار عراقي', 'success')
        return redirect(url_for('expenses'))
    
    # عرض المصروفات
    page = request.args.get('page', 1, type=int)
    expenses_list = Expense.query.order_by(Expense.date_added.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template('expenses.html', form=form, expenses=expenses_list)

@app.route('/debts', methods=['GET', 'POST'])
def debts():
    """صفحة إدارة الديون"""
    form = DebtForm()

    if form.validate_on_submit():
        # معالجة التواريخ
        start_date = None
        end_date = None
        if form.start_date.data:
            start_date = datetime.strptime(form.start_date.data, '%Y-%m-%d').date()
        if form.end_date.data:
            end_date = datetime.strptime(form.end_date.data, '%Y-%m-%d').date()

        new_debt = Debt(
            creditor_name=form.creditor_name.data,
            debt_type=form.debt_type.data,
            total_amount=float(form.total_amount.data),
            monthly_payment=float(form.monthly_payment.data) if form.monthly_payment.data else None,
            remaining_amount=float(form.total_amount.data),
            installments_count=form.installments_count.data,
            monthly_due_date=form.monthly_due_date.data,
            start_date=start_date,
            end_date=end_date,
            description=form.description.data
        )

        db.session.add(new_debt)
        db.session.commit()

        flash(f'تم حفظ الدين بنجاح! المبلغ: {form.total_amount.data:,.0f} دينار عراقي', 'success')
        return redirect(url_for('debts'))

    # عرض الديون
    active_debts = Debt.query.filter_by(status='active').order_by(Debt.date_added.desc()).all()

    return render_template('debts.html', form=form, debts=active_debts)

@app.route('/debt_payment', methods=['GET', 'POST'])
def debt_payment():
    """صفحة تسجيل دفعة دين"""
    form = DebtPaymentForm()

    # تحديث خيارات الديون النشطة
    active_debts = Debt.query.filter_by(status='active').all()
    form.debt_id.choices = [(debt.id, f"{debt.creditor_name} - {debt.debt_type}") for debt in active_debts]

    # تعيين التاريخ الحالي
    if request.method == 'GET':
        form.payment_date.data = datetime.now().strftime('%Y-%m-%d')

    if form.validate_on_submit():
        payment_date = datetime.strptime(form.payment_date.data, '%Y-%m-%d').date()

        # إنشاء الدفعة
        new_payment = DebtPayment(
            debt_id=form.debt_id.data,
            amount=float(form.amount.data),
            payment_date=payment_date,
            payment_type=form.payment_type.data,
            receipt_number=form.receipt_number.data,
            notes=form.notes.data
        )

        # تحديث المبلغ المتبقي والأقساط المدفوعة
        debt = Debt.query.get(form.debt_id.data)
        debt.remaining_amount -= float(form.amount.data)
        if form.payment_type.data == 'installment':
            debt.paid_installments += 1

        # تحديث حالة الدين إذا تم السداد بالكامل
        if debt.remaining_amount <= 0:
            debt.status = 'paid'
            debt.remaining_amount = 0

        db.session.add(new_payment)
        db.session.commit()

        flash(f'تم تسجيل الدفعة بنجاح! المبلغ: {form.amount.data:,.0f} دينار عراقي', 'success')
        return redirect(url_for('debts'))

    # عرض آخر الدفعات
    recent_payments = DebtPayment.query.order_by(DebtPayment.date_added.desc()).limit(10).all()

    return render_template('debt_payment.html', form=form, payments=recent_payments)

@app.route('/debt/<int:debt_id>')
def debt_details(debt_id):
    """تفاصيل دين محدد"""
    debt = Debt.query.get_or_404(debt_id)
    payments = DebtPayment.query.filter_by(debt_id=debt_id).order_by(DebtPayment.payment_date.desc()).all()

    # حساب الإحصائيات
    total_paid = sum(payment.amount for payment in payments)
    progress_percentage = (total_paid / debt.total_amount) * 100 if debt.total_amount > 0 else 0

    return render_template('debt_details.html', debt=debt, payments=payments,
                         total_paid=total_paid, progress_percentage=progress_percentage)

@app.route('/reports')
def reports():
    """صفحة التقارير والإحصائيات"""
    # إحصائيات شهرية
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    # إجمالي الرواتب هذا الشهر
    monthly_salary = db.session.query(db.func.sum(Salary.total_salary)).filter(
        Salary.month == current_month,
        Salary.year == current_year
    ).scalar() or 0
    
    # إجمالي المصروفات هذا الشهر
    monthly_expenses = db.session.query(db.func.sum(Expense.amount)).filter(
        db.extract('month', Expense.expense_date) == current_month,
        db.extract('year', Expense.expense_date) == current_year
    ).scalar() or 0
    
    # المصروفات حسب الفئة
    expenses_by_category = db.session.query(
        Expense.category,
        db.func.sum(Expense.amount)
    ).filter(
        db.extract('month', Expense.expense_date) == current_month,
        db.extract('year', Expense.expense_date) == current_year
    ).group_by(Expense.category).all()
    
    # الرصيد المتبقي
    balance = monthly_salary - monthly_expenses
    
    return render_template('reports.html',
                         monthly_salary=monthly_salary,
                         monthly_expenses=monthly_expenses,
                         balance=balance,
                         expenses_by_category=expenses_by_category)

@app.route('/api/expenses_chart')
def expenses_chart_api():
    """API لبيانات الرسم البياني للمصروفات"""
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    expenses_by_category = db.session.query(
        Expense.category,
        db.func.sum(Expense.amount)
    ).filter(
        db.extract('month', Expense.expense_date) == current_month,
        db.extract('year', Expense.expense_date) == current_year
    ).group_by(Expense.category).all()
    
    data = {
        'labels': [item[0] for item in expenses_by_category],
        'data': [float(item[1]) for item in expenses_by_category]
    }
    
    return jsonify(data)

# دوال مساعدة للقوالب
@app.template_filter('currency')
def currency_filter(amount):
    """تنسيق العملة"""
    return f"{amount:,.0f} د.ع"

@app.template_global()
def today():
    """الحصول على التاريخ الحالي"""
    return datetime.now().strftime('%Y-%m-%d')

@app.template_global()
def current_year():
    """الحصول على السنة الحالية"""
    return datetime.now().year

@app.template_global()
def current_month():
    """الحصول على الشهر الحالي"""
    return datetime.now().month

# إنشاء الجداول
with app.app_context():
    db.create_all()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
