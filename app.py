from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_wtf import FlaskForm
from wtforms import StringField, IntegerField, TextAreaField, SelectField, DecimalField
from wtforms.validators import DataRequired, NumberRange, Optional
from datetime import datetime
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'masroof-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///masroof.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# نماذج قاعدة البيانات
class Salary(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    first_salary = db.Column(db.Float, nullable=False)
    second_salary = db.Column(db.Float, default=0)
    total_salary = db.Column(db.Float, nullable=False)
    month = db.Column(db.Integer, nullable=False)
    year = db.Column(db.Integer, nullable=False)
    notes = db.Column(db.Text)
    date_added = db.Column(db.DateTime, default=datetime.utcnow)

class Expense(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Float, nullable=False)
    category = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    expense_date = db.Column(db.Date, nullable=False)
    date_added = db.Column(db.DateTime, default=datetime.utcnow)

class Debt(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    creditor_name = db.Column(db.String(100), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    remaining_amount = db.Column(db.Float, nullable=False)
    due_date = db.Column(db.Date)
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default='active')
    date_added = db.Column(db.DateTime, default=datetime.utcnow)

# نماذج الفورم
class SalaryForm(FlaskForm):
    first_salary = DecimalField('الراتب الأول (دينار عراقي)', validators=[DataRequired()], places=0)
    second_salary = DecimalField('الراتب الثاني (اختياري)', validators=[Optional()], places=0, default=0)
    month = SelectField('الشهر', choices=[
        (1, 'يناير'), (2, 'فبراير'), (3, 'مارس'), (4, 'أبريل'),
        (5, 'مايو'), (6, 'يونيو'), (7, 'يوليو'), (8, 'أغسطس'),
        (9, 'سبتمبر'), (10, 'أكتوبر'), (11, 'نوفمبر'), (12, 'ديسمبر')
    ], coerce=int, validators=[DataRequired()])
    year = IntegerField('السنة', validators=[DataRequired(), NumberRange(min=2020, max=2030)])
    notes = TextAreaField('ملاحظات (اختياري)')

class ExpenseForm(FlaskForm):
    amount = DecimalField('المبلغ (دينار عراقي)', validators=[DataRequired()], places=0)
    category = SelectField('الفئة', choices=[
        ('طعام', 'طعام وشراب'),
        ('مواصلات', 'مواصلات'),
        ('فواتير', 'فواتير'),
        ('ترفيه', 'ترفيه'),
        ('صحة', 'صحة'),
        ('تعليم', 'تعليم'),
        ('ملابس', 'ملابس'),
        ('أخرى', 'أخرى')
    ], validators=[DataRequired()])
    description = StringField('الوصف', validators=[DataRequired()])
    expense_date = StringField('تاريخ المصروف', validators=[DataRequired()])

class DebtForm(FlaskForm):
    creditor_name = StringField('اسم الدائن', validators=[DataRequired()])
    amount = DecimalField('مبلغ الدين (دينار عراقي)', validators=[DataRequired()], places=0)
    due_date = StringField('تاريخ الاستحقاق')
    description = TextAreaField('وصف الدين')

# الصفحات
@app.route('/')
def index():
    """الصفحة الرئيسية - لوحة المعلومات"""
    # إحصائيات سريعة
    latest_salary = Salary.query.order_by(Salary.date_added.desc()).first()
    total_expenses_month = db.session.query(db.func.sum(Expense.amount)).filter(
        db.extract('month', Expense.expense_date) == datetime.now().month,
        db.extract('year', Expense.expense_date) == datetime.now().year
    ).scalar() or 0
    
    active_debts = Debt.query.filter_by(status='active').count()
    total_debt_amount = db.session.query(db.func.sum(Debt.remaining_amount)).filter_by(status='active').scalar() or 0
    
    # آخر المعاملات
    recent_expenses = Expense.query.order_by(Expense.date_added.desc()).limit(5).all()
    
    return render_template('index.html', 
                         latest_salary=latest_salary,
                         total_expenses_month=total_expenses_month,
                         active_debts=active_debts,
                         total_debt_amount=total_debt_amount,
                         recent_expenses=recent_expenses)

@app.route('/salary', methods=['GET', 'POST'])
def salary():
    """صفحة إدارة الرواتب"""
    form = SalaryForm()
    
    # تعيين القيم الافتراضية
    if request.method == 'GET':
        form.month.data = datetime.now().month
        form.year.data = datetime.now().year
    
    if form.validate_on_submit():
        total = float(form.first_salary.data) + float(form.second_salary.data or 0)
        
        new_salary = Salary(
            first_salary=float(form.first_salary.data),
            second_salary=float(form.second_salary.data or 0),
            total_salary=total,
            month=form.month.data,
            year=form.year.data,
            notes=form.notes.data
        )
        
        db.session.add(new_salary)
        db.session.commit()
        
        flash(f'تم حفظ الراتب بنجاح! المجموع: {total:,.0f} دينار عراقي', 'success')
        return redirect(url_for('salary'))
    
    # عرض الرواتب المحفوظة
    salaries = Salary.query.order_by(Salary.date_added.desc()).all()
    
    return render_template('salary.html', form=form, salaries=salaries)

@app.route('/expenses', methods=['GET', 'POST'])
def expenses():
    """صفحة إدارة المصروفات"""
    form = ExpenseForm()

    # تعيين التاريخ الحالي كقيمة افتراضية
    if request.method == 'GET':
        form.expense_date.data = datetime.now().strftime('%Y-%m-%d')

    if form.validate_on_submit():
        expense_date = datetime.strptime(form.expense_date.data, '%Y-%m-%d').date()
        
        new_expense = Expense(
            amount=float(form.amount.data),
            category=form.category.data,
            description=form.description.data,
            expense_date=expense_date
        )
        
        db.session.add(new_expense)
        db.session.commit()
        
        flash(f'تم حفظ المصروف بنجاح! المبلغ: {form.amount.data:,.0f} دينار عراقي', 'success')
        return redirect(url_for('expenses'))
    
    # عرض المصروفات
    page = request.args.get('page', 1, type=int)
    expenses_list = Expense.query.order_by(Expense.date_added.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template('expenses.html', form=form, expenses=expenses_list)

@app.route('/debts', methods=['GET', 'POST'])
def debts():
    """صفحة إدارة الديون"""
    form = DebtForm()
    
    if form.validate_on_submit():
        due_date = None
        if form.due_date.data:
            due_date = datetime.strptime(form.due_date.data, '%Y-%m-%d').date()
        
        new_debt = Debt(
            creditor_name=form.creditor_name.data,
            amount=float(form.amount.data),
            remaining_amount=float(form.amount.data),
            due_date=due_date,
            description=form.description.data
        )
        
        db.session.add(new_debt)
        db.session.commit()
        
        flash(f'تم حفظ الدين بنجاح! المبلغ: {form.amount.data:,.0f} دينار عراقي', 'success')
        return redirect(url_for('debts'))
    
    # عرض الديون
    active_debts = Debt.query.filter_by(status='active').order_by(Debt.date_added.desc()).all()
    
    return render_template('debts.html', form=form, debts=active_debts)

@app.route('/reports')
def reports():
    """صفحة التقارير والإحصائيات"""
    # إحصائيات شهرية
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    # إجمالي الرواتب هذا الشهر
    monthly_salary = db.session.query(db.func.sum(Salary.total_salary)).filter(
        Salary.month == current_month,
        Salary.year == current_year
    ).scalar() or 0
    
    # إجمالي المصروفات هذا الشهر
    monthly_expenses = db.session.query(db.func.sum(Expense.amount)).filter(
        db.extract('month', Expense.expense_date) == current_month,
        db.extract('year', Expense.expense_date) == current_year
    ).scalar() or 0
    
    # المصروفات حسب الفئة
    expenses_by_category = db.session.query(
        Expense.category,
        db.func.sum(Expense.amount)
    ).filter(
        db.extract('month', Expense.expense_date) == current_month,
        db.extract('year', Expense.expense_date) == current_year
    ).group_by(Expense.category).all()
    
    # الرصيد المتبقي
    balance = monthly_salary - monthly_expenses
    
    return render_template('reports.html',
                         monthly_salary=monthly_salary,
                         monthly_expenses=monthly_expenses,
                         balance=balance,
                         expenses_by_category=expenses_by_category)

@app.route('/api/expenses_chart')
def expenses_chart_api():
    """API لبيانات الرسم البياني للمصروفات"""
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    expenses_by_category = db.session.query(
        Expense.category,
        db.func.sum(Expense.amount)
    ).filter(
        db.extract('month', Expense.expense_date) == current_month,
        db.extract('year', Expense.expense_date) == current_year
    ).group_by(Expense.category).all()
    
    data = {
        'labels': [item[0] for item in expenses_by_category],
        'data': [float(item[1]) for item in expenses_by_category]
    }
    
    return jsonify(data)

# دوال مساعدة للقوالب
@app.template_filter('currency')
def currency_filter(amount):
    """تنسيق العملة"""
    return f"{amount:,.0f} د.ع"

@app.template_global()
def today():
    """الحصول على التاريخ الحالي"""
    return datetime.now().strftime('%Y-%m-%d')

@app.template_global()
def current_year():
    """الحصول على السنة الحالية"""
    return datetime.now().year

@app.template_global()
def current_month():
    """الحصول على الشهر الحالي"""
    return datetime.now().month

# إنشاء الجداول
with app.app_context():
    db.create_all()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
