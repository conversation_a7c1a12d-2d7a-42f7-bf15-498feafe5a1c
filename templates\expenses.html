{% extends "base.html" %}

{% block title %}إدارة المصروفات - مصروف{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <h2 class="text-white mb-4">
            <i class="fas fa-shopping-cart me-2"></i>
            إدارة المصروفات
        </h2>
    </div>
</div>

<div class="row">
    <!-- نموذج إضافة مصروف جديد -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مصروف جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.amount.label(class="form-label") }}
                        {{ form.amount(class="form-control", placeholder="أدخل مبلغ المصروف") }}
                        {% if form.amount.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.amount.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.category.label(class="form-label") }}
                        {{ form.category(class="form-select") }}
                        {% if form.category.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.category.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", placeholder="وصف المصروف") }}
                        {% if form.description.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.description.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.expense_date.label(class="form-label") }}
                        {{ form.expense_date(class="form-control", type="date", value=today()) }}
                        {% if form.expense_date.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.expense_date.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>
                            حفظ المصروف
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                {% set total_expenses = expenses.items|sum(attribute='amount') if expenses.items else 0 %}
                {% set expenses_count = expenses.items|length if expenses.items else 0 %}
                
                <div class="text-center mb-3">
                    <h4 class="text-danger">{{ "{:,.0f}".format(total_expenses) }} د.ع</h4>
                    <small class="text-muted">إجمالي المصروفات</small>
                </div>
                
                <div class="text-center">
                    <h5 class="text-info">{{ expenses_count }}</h5>
                    <small class="text-muted">عدد المصروفات</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- قائمة المصروفات -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2 text-primary"></i>
                    المصروفات المسجلة
                </h5>
            </div>
            <div class="card-body">
                {% if expenses.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوصف</th>
                                    <th>الفئة</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الإضافة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for expense in expenses.items %}
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ expense.expense_date.strftime('%Y-%m-%d') }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong>{{ expense.description }}</strong>
                                    </td>
                                    <td>
                                        {% set category_colors = {
                                            'طعام': 'bg-warning',
                                            'مواصلات': 'bg-info',
                                            'فواتير': 'bg-danger',
                                            'ترفيه': 'bg-success',
                                            'صحة': 'bg-primary',
                                            'تعليم': 'bg-dark',
                                            'ملابس': 'bg-secondary',
                                            'أخرى': 'bg-light text-dark'
                                        } %}
                                        <span class="badge {{ category_colors.get(expense.category, 'bg-secondary') }}">
                                            {{ expense.category }}
                                        </span>
                                    </td>
                                    <td class="text-danger fw-bold">
                                        {{ "{:,.0f}".format(expense.amount) }} د.ع
                                    </td>
                                    <td class="text-muted">
                                        {{ expense.date_added.strftime('%Y-%m-%d %H:%M') }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if expenses.pages > 1 %}
                        <nav aria-label="صفحات المصروفات">
                            <ul class="pagination justify-content-center">
                                {% if expenses.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('expenses', page=expenses.prev_num) }}">السابق</a>
                                    </li>
                                {% endif %}
                                
                                {% for page_num in expenses.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != expenses.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('expenses', page=page_num) }}">{{ page_num }}</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if expenses.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('expenses', page=expenses.next_num) }}">التالي</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد مصروفات مسجلة بعد</h6>
                        <p class="text-muted">ابدأ بإضافة مصروفك الأول من النموذج على اليسار</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نصائح لإدارة المصروفات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح لإدارة المصروفات
                </h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-clock text-primary me-2 mt-1"></i>
                            <div>
                                <strong>سجل فوراً</strong>
                                <p class="text-muted small mb-0">سجل مصروفاتك فور حدوثها لتجنب النسيان</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-tags text-success me-2 mt-1"></i>
                            <div>
                                <strong>صنف بدقة</strong>
                                <p class="text-muted small mb-0">اختر الفئة المناسبة لكل مصروف لتحليل أفضل</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-edit text-info me-2 mt-1"></i>
                            <div>
                                <strong>وصف واضح</strong>
                                <p class="text-muted small mb-0">اكتب وصفاً واضحاً لكل مصروف للمراجعة لاحقاً</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-chart-line text-warning me-2 mt-1"></i>
                            <div>
                                <strong>راجع بانتظام</strong>
                                <p class="text-muted small mb-0">راجع مصروفاتك أسبوعياً لتتبع إنفاقك</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تعيين التاريخ الحالي كقيمة افتراضية
    document.addEventListener('DOMContentLoaded', function() {
        const dateInput = document.getElementById('expense_date');
        if (dateInput && !dateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            dateInput.value = today;
        }
    });
</script>
{% endblock %}
