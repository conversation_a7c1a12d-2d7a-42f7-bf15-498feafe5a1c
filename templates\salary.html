{% extends "base.html" %}

{% block title %}إدارة الرواتب - مصروف{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <h2 class="text-white mb-4">
            <i class="fas fa-money-bill-wave me-2"></i>
            إدارة الرواتب
        </h2>
    </div>
</div>

<div class="row">
    <!-- نموذج إضافة راتب جديد -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة راتب جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.first_salary.label(class="form-label") }}
                        {{ form.first_salary(class="form-control", placeholder="أدخل مبلغ الراتب الأول") }}
                        {% if form.first_salary.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.first_salary.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.second_salary.label(class="form-label") }}
                        {{ form.second_salary(class="form-control", placeholder="أدخل مبلغ الراتب الثاني (إن وجد)") }}
                        {% if form.second_salary.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.second_salary.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.month.label(class="form-label") }}
                            {{ form.month(class="form-select") }}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.year.label(class="form-label") }}
                            {{ form.year(class="form-control") }}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows="3", placeholder="أضف ملاحظات إضافية...") }}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الراتب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- قائمة الرواتب المحفوظة -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2 text-primary"></i>
                    الرواتب المحفوظة
                </h5>
            </div>
            <div class="card-body">
                {% if salaries %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الشهر/السنة</th>
                                    <th>الراتب الأول</th>
                                    <th>الراتب الثاني</th>
                                    <th>المجموع</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for salary in salaries %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">
                                            {{ salary.month }}/{{ salary.year }}
                                        </span>
                                    </td>
                                    <td class="text-success fw-bold">
                                        {{ "{:,.0f}".format(salary.first_salary) }} د.ع
                                    </td>
                                    <td class="text-info fw-bold">
                                        {{ "{:,.0f}".format(salary.second_salary) }} د.ع
                                    </td>
                                    <td class="text-primary fw-bold">
                                        {{ "{:,.0f}".format(salary.total_salary) }} د.ع
                                    </td>
                                    <td class="text-muted">
                                        {{ salary.date_added.strftime('%Y-%m-%d') }}
                                    </td>
                                    <td>
                                        {% if salary.notes %}
                                            <span class="text-muted" title="{{ salary.notes }}">
                                                {{ salary.notes[:30] }}{% if salary.notes|length > 30 %}...{% endif %}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- إحصائيات سريعة -->
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <h6 class="text-muted mb-1">إجمالي الرواتب</h6>
                                <h4 class="text-primary mb-0">
                                    {{ "{:,.0f}".format(salaries|sum(attribute='total_salary')) }} د.ع
                                </h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <h6 class="text-muted mb-1">متوسط الراتب</h6>
                                <h4 class="text-success mb-0">
                                    {{ "{:,.0f}".format((salaries|sum(attribute='total_salary')) / (salaries|length)) }} د.ع
                                </h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <h6 class="text-muted mb-1">عدد الرواتب</h6>
                                <h4 class="text-info mb-0">{{ salaries|length }}</h4>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد رواتب مسجلة بعد</h6>
                        <p class="text-muted">ابدأ بإضافة راتبك الأول من النموذج على اليسار</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح مفيدة
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                            <div>
                                <strong>سجل رواتبك بانتظام</strong>
                                <p class="text-muted small mb-0">احرص على تسجيل راتبك فور استلامه لتتبع دخلك بدقة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calculator text-info me-2 mt-1"></i>
                            <div>
                                <strong>استخدم الراتب الثاني</strong>
                                <p class="text-muted small mb-0">إذا كان لديك مصادر دخل إضافية، سجلها في خانة الراتب الثاني</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-sticky-note text-warning me-2 mt-1"></i>
                            <div>
                                <strong>أضف ملاحظات</strong>
                                <p class="text-muted small mb-0">استخدم حقل الملاحظات لتسجيل تفاصيل إضافية مهمة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث المجموع تلقائياً
    document.addEventListener('DOMContentLoaded', function() {
        const firstSalary = document.getElementById('first_salary');
        const secondSalary = document.getElementById('second_salary');
        
        function updateTotal() {
            const first = parseFloat(firstSalary.value) || 0;
            const second = parseFloat(secondSalary.value) || 0;
            const total = first + second;
            
            // يمكن إضافة عرض المجموع هنا إذا أردت
        }
        
        if (firstSalary && secondSalary) {
            firstSalary.addEventListener('input', updateTotal);
            secondSalary.addEventListener('input', updateTotal);
        }
    });
</script>
{% endblock %}
