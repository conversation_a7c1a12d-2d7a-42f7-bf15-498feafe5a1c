# 💰 مصروف - تطبيق إدارة الرواتب والمصروفات الشخصية

تطبيق ويب حديث ومتكامل لإدارة الأموال الشخصية باللغة العربية، مبني بتقنيات حديثة ومصمم خصيصاً للمستخدمين العرب.

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![Flask](https://img.shields.io/badge/Flask-3.0+-green.svg)
![Bootstrap](https://img.shields.io/badge/Bootstrap-5.3-purple.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## 🌟 المميزات الرئيسية

### ✅ **إدارة الرواتب**
- تسجيل الراتب الأول والثاني
- تتبع الرواتب الشهرية
- إحصائيات شاملة للدخل
- دعم العملة العراقية (دينار عراقي)

### ✅ **إدارة المصروفات**
- تصنيف المصروفات حسب الفئات
- تتبع المصروفات اليومية
- رسوم بيانية تفاعلية
- تحليل أنماط الإنفاق

### ✅ **إدارة الديون والأقساط**
- تسجيل الديون والقروض (سيارة، منزل، شخصي، إلخ)
- نظام أقساط متقدم مع تتبع الدفعات الشهرية
- تسجيل الدفعات مع أرقام الإيصالات
- تتبع مواعيد الاستحقاق الشهرية
- شريط تقدم السداد
- إحصائيات شاملة للديون والدفعات

### ✅ **التقارير والتحليل**
- تقارير مالية شاملة
- رسوم بيانية تفاعلية
- تحليل الوضع المالي
- نصائح مالية ذكية

### ✅ **واجهة مستخدم متقدمة**
- تصميم عربي متجاوب
- دعم كامل للغة العربية
- واجهة حديثة مع Bootstrap 5
- تجربة مستخدم سلسة

## 🛠️ التقنيات المستخدمة

- **Backend**: Python 3.8+ مع Flask
- **Frontend**: HTML5, CSS3, JavaScript
- **UI Framework**: Bootstrap 5.3 (RTL)
- **Database**: SQLite
- **Charts**: Chart.js
- **Icons**: Font Awesome 6
- **Fonts**: Google Fonts (Cairo)

## 📋 متطلبات التشغيل

- Python 3.8 أو أحدث
- pip (مدير حزم Python)
- متصفح ويب حديث

## 🚀 التثبيت والتشغيل

### الطريقة الأولى: تشغيل تلقائي (Windows)
```bash
# انقر مرتين على الملف
install_and_run.bat
```

### الطريقة الثانية: تشغيل يدوي
```bash
# 1. تثبيت المتطلبات
pip install -r requirements.txt

# 2. تشغيل التطبيق
python run.py
```

### الطريقة الثالثة: تشغيل مباشر
```bash
python app.py
```

## 🌐 الوصول للتطبيق

بعد التشغيل، افتح متصفحك وانتقل إلى:
```
http://localhost:5000
```

## 📱 صفحات التطبيق

| الصفحة | الرابط | الوصف |
|---------|---------|--------|
| 🏠 الرئيسية | `/` | لوحة المعلومات والإحصائيات |
| 💰 الرواتب | `/salary` | إدارة وتتبع الرواتب |
| 🛒 المصروفات | `/expenses` | تسجيل وتتبع المصروفات |
| 💳 الديون | `/debts` | إدارة الديون والأقساط |
| 💸 دفعات الديون | `/debt_payment` | تسجيل دفعات الأقساط |
| 📄 تفاصيل الدين | `/debt/<id>` | تفاصيل دين محدد ودفعاته |
| 📊 التقارير | `/reports` | التقارير والتحليلات المالية |

## 📁 هيكل المشروع

```
masroof/
├── app.py                 # التطبيق الرئيسي
├── run.py                 # ملف التشغيل
├── requirements.txt       # متطلبات Python
├── install_and_run.bat   # ملف التشغيل التلقائي
├── README.md             # هذا الملف
├── templates/            # قوالب HTML
│   ├── base.html         # القالب الأساسي
│   ├── index.html        # الصفحة الرئيسية
│   ├── salary.html       # صفحة الرواتب
│   ├── expenses.html     # صفحة المصروفات
│   ├── debts.html        # صفحة الديون
│   └── reports.html      # صفحة التقارير
└── masroof.db           # قاعدة البيانات (تُنشأ تلقائياً)
```

## 🎨 لقطات الشاشة

### لوحة المعلومات
- عرض الإحصائيات السريعة
- آخر المعاملات
- إجراءات سريعة

### إدارة الرواتب
- نموذج إضافة راتب جديد
- قائمة الرواتب المحفوظة
- إحصائيات الرواتب

### إدارة المصروفات
- تصنيف المصروفات
- تتبع الإنفاق
- رسوم بيانية

### التقارير
- تحليل مالي شامل
- رسوم بيانية تفاعلية
- نصائح مالية

## 🔧 التخصيص والتطوير

### إضافة فئات مصروفات جديدة
```python
# في app.py - ExpenseForm
category = SelectField('الفئة', choices=[
    ('فئة_جديدة', 'اسم الفئة الجديدة'),
    # ... باقي الفئات
])
```

### تغيير العملة
```python
# في app.py - تغيير "دينار عراقي" إلى العملة المطلوبة
```

### إضافة ميزات جديدة
1. أضف النموذج في `app.py`
2. أنشئ القالب في `templates/`
3. أضف الرابط في `base.html`

## 🐛 استكشاف الأخطاء

### مشكلة في تثبيت المتطلبات
```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت المتطلبات مرة أخرى
pip install -r requirements.txt
```

### مشكلة في الوصول للتطبيق
- تأكد من أن المنفذ 5000 غير مستخدم
- جرب منفذ آخر: `python app.py --port 8000`

### مشكلة في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm masroof.db
python app.py
```

## 🤝 المساهمة

نرحب بمساهماتكم! يمكنكم:

1. **الإبلاغ عن الأخطاء**: افتحوا issue جديد
2. **اقتراح ميزات**: شاركوا أفكاركم
3. **تحسين الكود**: أرسلوا pull request
4. **تحسين التوثيق**: ساعدوا في تحسين الشرح

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👨‍💻 المطور

تم تطوير هذا التطبيق بواسطة الذكاء الاصطناعي لمساعدة المستخدمين العرب في إدارة أموالهم الشخصية.

## 🙏 شكر وتقدير

- **Bootstrap Team** - للإطار الرائع
- **Flask Team** - لإطار العمل المرن
- **Font Awesome** - للأيقونات الجميلة
- **Chart.js** - للرسوم البيانية التفاعلية

---

## 📞 الدعم

إذا واجهتم أي مشاكل أو لديكم اقتراحات، لا تترددوا في التواصل!

**استمتعوا بإدارة أموالكم مع تطبيق مصروف! 💰✨**
