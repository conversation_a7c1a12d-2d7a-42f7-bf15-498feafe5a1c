# مصروف - تطبيق إدارة الرواتب والمصروفات الشخصية

تطبيق مكتبي باللغة العربية لإدارة الرواتب والمصروفات والديون الشخصية، مبني بـ Python و CustomTkinter.

## المميزات الحالية

### ✅ المتوفرة الآن:
- **الواجهة الرئيسية**: لوحة معلومات جميلة وسهلة الاستخدام
- **إدخال الرواتب**: نافذة مخصصة لإدخال الراتب الأول والثاني
- **دعم العملة العراقية**: تعامل كامل مع الدينار العراقي
- **قاعدة بيانات محلية**: حفظ آمن للبيانات باستخدام SQLite
- **واجهة عربية**: دعم كامل للغة العربية

### 🔄 قيد التطوير:
- إدارة المصروفات
- إدارة الديون
- التقارير والتحليلات
- الذكاء الصناعي للتنبؤات المالية

## متطلبات التشغيل

- Python 3.11 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

## التثبيت والتشغيل

### 1. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق:
```bash
python main.py
```

## البنية التقنية

- **اللغة**: Python 3.11+
- **واجهة المستخدم**: CustomTkinter
- **قاعدة البيانات**: SQLite
- **التحليل**: pandas, numpy, scikit-learn
- **التقارير**: matplotlib, plotly
- **الإشعارات**: plyer

## كيفية الاستخدام

1. **تشغيل التطبيق**: قم بتشغيل `python main.py`
2. **إدخال الراتب**: اضغط على "إدخال الراتب" من القائمة الجانبية
3. **ملء البيانات**: أدخل الراتب الأول والثاني (اختياري)
4. **اختيار الشهر والسنة**: حدد الشهر والسنة المناسبة
5. **إضافة ملاحظات**: يمكنك إضافة ملاحظات إضافية
6. **حفظ البيانات**: اضغط "حفظ الراتب"

## هيكل الملفات

```
masroof/
├── main.py              # الملف الرئيسي للتطبيق
├── database.py          # إدارة قاعدة البيانات
├── requirements.txt     # متطلبات Python
├── README.md           # هذا الملف
└── masroof.db          # قاعدة البيانات (تُنشأ تلقائياً)
```

## المساهمة

هذا مشروع شخصي لإدارة الأموال. يمكنك تطويره وتخصيصه حسب احتياجاتك.

## الترخيص

مشروع شخصي مفتوح المصدر.
