# إعدادات التطبيق
APP_NAME = "مصروف - إدارة الرواتب والمصروفات الشخصية"
APP_VERSION = "1.0.0"
DATABASE_NAME = "masroof.db"

# إعدادات الواجهة
WINDOW_WIDTH = 900
WINDOW_HEIGHT = 700
SALARY_WINDOW_WIDTH = 500
SALARY_WINDOW_HEIGHT = 600

# إعدادات الألوان والمظهر
APPEARANCE_MODE = "dark"  # "light", "dark", "system"
COLOR_THEME = "blue"      # "blue", "green", "dark-blue"

# إعدادات العملة
CURRENCY_SYMBOL = "دينار عراقي"
CURRENCY_CODE = "IQD"

# إعدادات التاريخ
MONTHS_ARABIC = [
    "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
    "يوليو", "أغسطس", "سب<PERSON>م<PERSON><PERSON>", "أكتوبر", "نوفمبر", "ديسمبر"
]

# إعدادات قاعدة البيانات
DB_BACKUP_ENABLED = True
DB_BACKUP_INTERVAL_DAYS = 7

# إعدادات الإشعارات
NOTIFICATIONS_ENABLED = True
SALARY_REMINDER_ENABLED = True
DEBT_REMINDER_ENABLED = True
