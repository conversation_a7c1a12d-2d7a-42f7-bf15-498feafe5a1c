# إعدادات التطبيق
import os

class Config:
    """إعدادات التطبيق الأساسية"""
    
    # إعدادات Flask
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'masroof-secret-key-2024-arabic-finance-app'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///masroof.db'
    
    # إعدادات التطبيق
    APP_NAME = 'مصروف - إدارة الرواتب والمصروفات الشخصية'
    APP_VERSION = '2.0.0'
    APP_DESCRIPTION = 'تطبيق ويب متكامل لإدارة الأموال الشخصية باللغة العربية'
    
    # إعدادات العملة
    CURRENCY_NAME = 'دينار عراقي'
    CURRENCY_CODE = 'IQD'
    CURRENCY_SYMBOL = 'د.ع'
    
    # إعدادات اللغة
    LANGUAGE = 'ar'
    TIMEZONE = 'Asia/Baghdad'
    
    # إعدادات الواجهة
    ITEMS_PER_PAGE = 10
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # ساعة واحدة
    
    # إعدادات التطوير
    DEBUG = True
    TESTING = False

class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///masroof_dev.db'

class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///masroof_prod.db'
    SECRET_KEY = os.environ.get('SECRET_KEY')
    
    # إعدادات أمان إضافية للإنتاج
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# قاموس البيئات
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

# إعدادات إضافية للتطبيق
class AppSettings:
    """إعدادات إضافية للتطبيق"""
    
    # فئات المصروفات
    EXPENSE_CATEGORIES = [
        ('طعام', 'طعام وشراب'),
        ('مواصلات', 'مواصلات'),
        ('فواتير', 'فواتير'),
        ('ترفيه', 'ترفيه'),
        ('صحة', 'صحة'),
        ('تعليم', 'تعليم'),
        ('ملابس', 'ملابس'),
        ('منزل', 'مستلزمات منزلية'),
        ('سيارة', 'صيانة السيارة'),
        ('هدايا', 'هدايا ومناسبات'),
        ('أخرى', 'أخرى')
    ]
    
    # الأشهر العربية
    ARABIC_MONTHS = [
        (1, 'يناير'), (2, 'فبراير'), (3, 'مارس'), (4, 'أبريل'),
        (5, 'مايو'), (6, 'يونيو'), (7, 'يوليو'), (8, 'أغسطس'),
        (9, 'سبتمبر'), (10, 'أكتوبر'), (11, 'نوفمبر'), (12, 'ديسمبر')
    ]
    
    # حالات الديون
    DEBT_STATUSES = [
        ('active', 'نشط'),
        ('paid', 'مُسدد'),
        ('overdue', 'متأخر'),
        ('cancelled', 'ملغي')
    ]
    
    # ألوان الرسوم البيانية
    CHART_COLORS = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF',
        '#4BC0C0', '#FF6384', '#36A2EB', '#FFCE56'
    ]
    
    # إعدادات التقارير
    REPORT_SETTINGS = {
        'default_period': 'monthly',
        'chart_height': 400,
        'chart_width': 600,
        'export_formats': ['pdf', 'excel', 'csv']
    }
    
    # رسائل النظام
    SYSTEM_MESSAGES = {
        'success': {
            'salary_added': 'تم حفظ الراتب بنجاح!',
            'expense_added': 'تم حفظ المصروف بنجاح!',
            'debt_added': 'تم حفظ الدين بنجاح!',
            'data_updated': 'تم تحديث البيانات بنجاح!',
            'data_deleted': 'تم حذف البيانات بنجاح!'
        },
        'error': {
            'invalid_data': 'البيانات المدخلة غير صحيحة',
            'required_fields': 'يرجى ملء جميع الحقول المطلوبة',
            'database_error': 'حدث خطأ في قاعدة البيانات',
            'permission_denied': 'ليس لديك صلاحية للوصول',
            'not_found': 'البيانات المطلوبة غير موجودة'
        },
        'warning': {
            'data_exists': 'البيانات موجودة مسبقاً',
            'confirm_delete': 'هل أنت متأكد من الحذف؟',
            'unsaved_changes': 'لديك تغييرات غير محفوظة'
        },
        'info': {
            'no_data': 'لا توجد بيانات للعرض',
            'loading': 'جاري التحميل...',
            'processing': 'جاري المعالجة...'
        }
    }
    
    # نصائح مالية
    FINANCIAL_TIPS = [
        'احرص على توفير 20% من راتبك الشهري',
        'تتبع مصروفاتك اليومية لتحكم أفضل في الميزانية',
        'أنشئ صندوق طوارئ يكفي لـ 3-6 أشهر من المصروفات',
        'راجع ميزانيتك شهرياً وعدّل حسب الحاجة',
        'تجنب الديون غير الضرورية',
        'استثمر في تعليمك وتطوير مهاراتك',
        'قارن الأسعار قبل الشراء',
        'استخدم قائمة التسوق لتجنب الشراء العشوائي'
    ]
    
    # إعدادات الإشعارات
    NOTIFICATION_SETTINGS = {
        'debt_reminder_days': 7,  # تذكير بالديون قبل 7 أيام
        'monthly_report': True,   # تقرير شهري
        'budget_alert': True,     # تنبيه تجاوز الميزانية
        'savings_goal': True      # تنبيه أهداف التوفير
    }

# دالة للحصول على الإعدادات
def get_config(config_name=None):
    """الحصول على إعدادات التطبيق"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    return config.get(config_name, config['default'])
