{% extends "base.html" %}

{% block title %}التقارير والإحصائيات - مصروف{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <h2 class="text-white mb-4">
            <i class="fas fa-chart-bar me-2"></i>
            التقارير والإحصائيات
        </h2>
    </div>
</div>

<!-- الإحصائيات الشهرية -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-money-bill-wave fa-2x text-success mb-3"></i>
                <h4 class="text-success">{{ "{:,.0f}".format(monthly_salary) }} د.ع</h4>
                <h6 class="card-title">إجمالي الرواتب</h6>
                <small class="text-muted">هذا الشهر</small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-shopping-cart fa-2x text-danger mb-3"></i>
                <h4 class="text-danger">{{ "{:,.0f}".format(monthly_expenses) }} د.ع</h4>
                <h6 class="card-title">إجمالي المصروفات</h6>
                <small class="text-muted">هذا الشهر</small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-piggy-bank fa-2x {% if balance >= 0 %}text-success{% else %}text-danger{% endif %} mb-3"></i>
                <h4 class="{% if balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                    {{ "{:,.0f}".format(balance) }} د.ع
                </h4>
                <h6 class="card-title">الرصيد المتبقي</h6>
                <small class="text-muted">الراتب - المصروفات</small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-percentage fa-2x text-info mb-3"></i>
                <h4 class="text-info">
                    {% if monthly_salary > 0 %}
                        {{ "{:.1f}".format((monthly_expenses / monthly_salary) * 100) }}%
                    {% else %}
                        0%
                    {% endif %}
                </h4>
                <h6 class="card-title">نسبة الإنفاق</h6>
                <small class="text-muted">من إجمالي الراتب</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الرسم البياني للمصروفات -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2 text-primary"></i>
                    توزيع المصروفات حسب الفئة
                </h5>
            </div>
            <div class="card-body">
                {% if expenses_by_category %}
                    <canvas id="expensesChart" width="400" height="200"></canvas>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد بيانات للعرض</h6>
                        <p class="text-muted">أضف بعض المصروفات لعرض الرسم البياني</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- تفاصيل المصروفات حسب الفئة -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2 text-primary"></i>
                    تفاصيل المصروفات
                </h5>
            </div>
            <div class="card-body">
                {% if expenses_by_category %}
                    {% for category, amount in expenses_by_category %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-1">{{ category }}</h6>
                                <small class="text-muted">
                                    {% if monthly_expenses > 0 %}
                                        {{ "{:.1f}".format((amount / monthly_expenses) * 100) }}% من الإجمالي
                                    {% endif %}
                                </small>
                            </div>
                            <div class="text-end">
                                <strong class="text-danger">{{ "{:,.0f}".format(amount) }} د.ع</strong>
                            </div>
                        </div>
                        <div class="progress mb-3" style="height: 6px;">
                            <div class="progress-bar" role="progressbar" 
                                 style="width: {% if monthly_expenses > 0 %}{{ (amount / monthly_expenses) * 100 }}{% else %}0{% endif %}%">
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-info-circle text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد مصروفات هذا الشهر</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- تحليل مالي إضافي -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-analytics me-2 text-primary"></i>
                    التحليل المالي
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">تقييم الوضع المالي:</h6>
                        {% if balance > 0 %}
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>وضع مالي جيد!</strong> لديك فائض قدره {{ "{:,.0f}".format(balance) }} دينار عراقي هذا الشهر.
                            </div>
                        {% elif balance == 0 %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>توازن مالي!</strong> مصروفاتك تساوي راتبك تماماً هذا الشهر.
                            </div>
                        {% else %}
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <strong>تحذير!</strong> مصروفاتك تزيد عن راتبك بمقدار {{ "{:,.0f}".format(-balance) }} دينار عراقي.
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="text-primary">نصائح مالية:</h6>
                        <ul class="list-unstyled">
                            {% if monthly_expenses > 0 and monthly_salary > 0 %}
                                {% set expense_ratio = (monthly_expenses / monthly_salary) * 100 %}
                                {% if expense_ratio > 80 %}
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>نسبة إنفاقك عالية ({{ "{:.1f}".format(expense_ratio) }}%)</li>
                                    <li><i class="fas fa-lightbulb text-info me-2"></i>حاول تقليل المصروفات غير الضرورية</li>
                                {% elif expense_ratio > 60 %}
                                    <li><i class="fas fa-info-circle text-info me-2"></i>نسبة إنفاق معتدلة ({{ "{:.1f}".format(expense_ratio) }}%)</li>
                                    <li><i class="fas fa-piggy-bank text-success me-2"></i>حاول توفير 20% من راتبك</li>
                                {% else %}
                                    <li><i class="fas fa-check-circle text-success me-2"></i>نسبة إنفاق ممتازة ({{ "{:.1f}".format(expense_ratio) }}%)</li>
                                    <li><i class="fas fa-chart-line text-primary me-2"></i>فكر في استثمار الفائض</li>
                                {% endif %}
                            {% endif %}
                            
                            {% if expenses_by_category %}
                                {% set max_category = expenses_by_category|max(attribute=1) %}
                                <li><i class="fas fa-tag text-warning me-2"></i>أكبر فئة إنفاق: {{ max_category[0] }}</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أهداف مالية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bullseye me-2 text-primary"></i>
                    أهداف مالية مقترحة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-piggy-bank fa-2x text-success mb-2"></i>
                            <h6>هدف التوفير</h6>
                            <p class="text-muted small">
                                {% if monthly_salary > 0 %}
                                    {{ "{:,.0f}".format(monthly_salary * 0.2) }} د.ع شهرياً
                                {% else %}
                                    حدد راتبك أولاً
                                {% endif %}
                            </p>
                            <small class="text-muted">20% من الراتب</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-shield-alt fa-2x text-info mb-2"></i>
                            <h6>صندوق الطوارئ</h6>
                            <p class="text-muted small">
                                {% if monthly_salary > 0 %}
                                    {{ "{:,.0f}".format(monthly_salary * 3) }} د.ع
                                {% else %}
                                    حدد راتبك أولاً
                                {% endif %}
                            </p>
                            <small class="text-muted">3 أشهر من الراتب</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-chart-line fa-2x text-warning mb-2"></i>
                            <h6>هدف الاستثمار</h6>
                            <p class="text-muted small">
                                {% if monthly_salary > 0 %}
                                    {{ "{:,.0f}".format(monthly_salary * 0.1) }} د.ع شهرياً
                                {% else %}
                                    حدد راتبك أولاً
                                {% endif %}
                            </p>
                            <small class="text-muted">10% من الراتب</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // رسم بياني للمصروفات
    {% if expenses_by_category %}
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('expensesChart').getContext('2d');
        
        const data = {
            labels: [{% for category, amount in expenses_by_category %}'{{ category }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for category, amount in expenses_by_category %}{{ amount }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40',
                    '#FF6384',
                    '#C9CBCF'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };
        
        const config = {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return label + ': ' + value.toLocaleString() + ' د.ع (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        };
        
        new Chart(ctx, config);
    });
    {% endif %}
</script>
{% endblock %}
