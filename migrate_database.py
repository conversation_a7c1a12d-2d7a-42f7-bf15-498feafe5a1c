#!/usr/bin/env python3
"""
سكريبت تحديث قاعدة البيانات لنظام الديون الجديد
Database Migration Script for New Debt System
"""

import sqlite3
import os
from datetime import datetime

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    if os.path.exists('masroof.db'):
        backup_name = f'masroof_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        import shutil
        shutil.copy2('masroof.db', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    return None

def check_table_structure():
    """فحص هيكل الجداول الحالية"""
    conn = sqlite3.connect('masroof.db')
    cursor = conn.cursor()
    
    try:
        # فحص جدول الديون
        cursor.execute("PRAGMA table_info(debt)")
        debt_columns = [row[1] for row in cursor.fetchall()]
        print(f"📋 أعمدة جدول الديون الحالية: {debt_columns}")
        
        # فحص وجود جدول الدفعات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='debt_payment'")
        payment_table_exists = cursor.fetchone() is not None
        print(f"📋 جدول الدفعات موجود: {payment_table_exists}")
        
        return debt_columns, payment_table_exists
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {e}")
        return [], False
    finally:
        conn.close()

def migrate_debt_table():
    """تحديث جدول الديون"""
    conn = sqlite3.connect('masroof.db')
    cursor = conn.cursor()
    
    try:
        print("🔄 تحديث جدول الديون...")
        
        # إضافة الأعمدة الجديدة إذا لم تكن موجودة
        new_columns = [
            ("debt_type", "VARCHAR(50) NOT NULL DEFAULT 'شخصي'"),
            ("total_amount", "FLOAT"),
            ("monthly_payment", "FLOAT"),
            ("installments_count", "INTEGER"),
            ("paid_installments", "INTEGER DEFAULT 0"),
            ("monthly_due_date", "INTEGER"),
            ("start_date", "DATE"),
            ("end_date", "DATE")
        ]
        
        for column_name, column_def in new_columns:
            try:
                cursor.execute(f"ALTER TABLE debt ADD COLUMN {column_name} {column_def}")
                print(f"✅ تم إضافة العمود: {column_name}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e):
                    print(f"ℹ️ العمود موجود مسبقاً: {column_name}")
                else:
                    print(f"⚠️ خطأ في إضافة العمود {column_name}: {e}")
        
        # تحديث البيانات الموجودة
        cursor.execute("""
            UPDATE debt 
            SET total_amount = amount 
            WHERE total_amount IS NULL AND amount IS NOT NULL
        """)
        
        # إزالة العمود القديم amount إذا كان موجوداً (سنتركه للتوافق)
        
        conn.commit()
        print("✅ تم تحديث جدول الديون بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث جدول الديون: {e}")
        conn.rollback()
    finally:
        conn.close()

def create_payment_table():
    """إنشاء جدول الدفعات"""
    conn = sqlite3.connect('masroof.db')
    cursor = conn.cursor()
    
    try:
        print("🔄 إنشاء جدول الدفعات...")
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS debt_payment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                debt_id INTEGER NOT NULL,
                amount FLOAT NOT NULL,
                payment_date DATE NOT NULL,
                payment_type VARCHAR(50) DEFAULT 'installment',
                receipt_number VARCHAR(100),
                notes TEXT,
                date_added DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (debt_id) REFERENCES debt (id)
            )
        """)
        
        conn.commit()
        print("✅ تم إنشاء جدول الدفعات بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول الدفعات: {e}")
    finally:
        conn.close()

def verify_migration():
    """التحقق من نجاح التحديث"""
    conn = sqlite3.connect('masroof.db')
    cursor = conn.cursor()
    
    try:
        print("🔍 التحقق من التحديث...")
        
        # فحص جدول الديون
        cursor.execute("PRAGMA table_info(debt)")
        debt_columns = [row[1] for row in cursor.fetchall()]
        
        required_columns = ['debt_type', 'total_amount', 'monthly_payment', 'installments_count']
        missing_columns = [col for col in required_columns if col not in debt_columns]
        
        if missing_columns:
            print(f"❌ أعمدة مفقودة في جدول الديون: {missing_columns}")
            return False
        
        # فحص جدول الدفعات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='debt_payment'")
        if not cursor.fetchone():
            print("❌ جدول الدفعات غير موجود")
            return False
        
        print("✅ تم التحديث بنجاح - جميع الجداول والأعمدة موجودة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False
    finally:
        conn.close()

def reset_database():
    """إعادة إنشاء قاعدة البيانات من الصفر"""
    print("🔄 إعادة إنشاء قاعدة البيانات...")
    
    # حذف قاعدة البيانات القديمة
    if os.path.exists('masroof.db'):
        backup_database()
        os.remove('masroof.db')
        print("✅ تم حذف قاعدة البيانات القديمة")
    
    # إنشاء قاعدة البيانات الجديدة
    try:
        from app import app, db
        with app.app_context():
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات الجديدة")
            return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔄 تحديث قاعدة البيانات - نظام الديون الجديد")
    print("=" * 60)
    print()
    
    if not os.path.exists('masroof.db'):
        print("ℹ️ قاعدة البيانات غير موجودة - سيتم إنشاؤها")
        if reset_database():
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
        return
    
    # فحص الهيكل الحالي
    debt_columns, payment_table_exists = check_table_structure()
    
    # تحديد نوع التحديث المطلوب
    needs_migration = 'debt_type' not in debt_columns
    needs_payment_table = not payment_table_exists
    
    if not needs_migration and not needs_payment_table:
        print("ℹ️ قاعدة البيانات محدثة بالفعل")
        return
    
    print(f"📋 التحديث المطلوب:")
    print(f"   - تحديث جدول الديون: {'نعم' if needs_migration else 'لا'}")
    print(f"   - إنشاء جدول الدفعات: {'نعم' if needs_payment_table else 'لا'}")
    print()
    
    # خيارات التحديث
    print("اختر طريقة التحديث:")
    print("1. تحديث تدريجي (الحفاظ على البيانات الموجودة)")
    print("2. إعادة إنشاء كاملة (حذف البيانات القديمة)")
    print("3. إلغاء")
    
    choice = input("اختر (1/2/3): ").strip()
    
    if choice == "1":
        # تحديث تدريجي
        backup_database()
        
        if needs_migration:
            migrate_debt_table()
        
        if needs_payment_table:
            create_payment_table()
        
        if verify_migration():
            print("\n🎉 تم التحديث بنجاح!")
        else:
            print("\n❌ فشل التحديث")
            
    elif choice == "2":
        # إعادة إنشاء كاملة
        if reset_database():
            print("\n🎉 تم إنشاء قاعدة البيانات الجديدة بنجاح!")
        else:
            print("\n❌ فشل في إنشاء قاعدة البيانات")
            
    else:
        print("تم الإلغاء")

if __name__ == "__main__":
    main()
