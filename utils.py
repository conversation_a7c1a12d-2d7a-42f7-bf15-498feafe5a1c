# أدوات مساعدة للتطبيق
from datetime import datetime, date
import locale
from config import AppSettings

def format_currency(amount, currency_symbol="د.ع"):
    """تنسيق العملة بالطريقة العربية"""
    try:
        # تنسيق الرقم بالفواصل
        formatted_amount = f"{amount:,.0f}"
        return f"{formatted_amount} {currency_symbol}"
    except:
        return f"0 {currency_symbol}"

def format_date_arabic(date_obj):
    """تنسيق التاريخ بالطريقة العربية"""
    if not date_obj:
        return ""
    
    try:
        if isinstance(date_obj, str):
            date_obj = datetime.strptime(date_obj, '%Y-%m-%d').date()
        
        # الحصول على اسم الشهر العربي
        month_name = get_arabic_month_name(date_obj.month)
        return f"{date_obj.day} {month_name} {date_obj.year}"
    except:
        return str(date_obj)

def get_arabic_month_name(month_number):
    """الحصول على اسم الشهر العربي"""
    months = dict(AppSettings.ARABIC_MONTHS)
    return months.get(month_number, "")

def calculate_percentage(part, total):
    """حساب النسبة المئوية"""
    if total == 0:
        return 0
    return round((part / total) * 100, 1)

def get_financial_status(income, expenses):
    """تحديد الحالة المالية"""
    if income == 0:
        return "غير محدد", "secondary"
    
    balance = income - expenses
    percentage = (expenses / income) * 100
    
    if balance > 0:
        if percentage <= 50:
            return "ممتاز", "success"
        elif percentage <= 70:
            return "جيد", "info"
        elif percentage <= 85:
            return "مقبول", "warning"
        else:
            return "حذر", "warning"
    else:
        return "خطر", "danger"

def get_expense_category_color(category):
    """الحصول على لون فئة المصروف"""
    colors = {
        'طعام': 'warning',
        'مواصلات': 'info',
        'فواتير': 'danger',
        'ترفيه': 'success',
        'صحة': 'primary',
        'تعليم': 'dark',
        'ملابس': 'secondary',
        'منزل': 'info',
        'سيارة': 'warning',
        'هدايا': 'success',
        'أخرى': 'light'
    }
    return colors.get(category, 'secondary')

def get_debt_status_info(debt):
    """الحصول على معلومات حالة الدين"""
    if not debt.due_date:
        return "غير محدد", "secondary"
    
    today = date.today()
    days_diff = (debt.due_date - today).days
    
    if days_diff < 0:
        return f"متأخر {abs(days_diff)} يوم", "danger"
    elif days_diff == 0:
        return "مستحق اليوم", "warning"
    elif days_diff <= 7:
        return f"مستحق خلال {days_diff} أيام", "warning"
    else:
        return f"مستحق خلال {days_diff} يوم", "success"

def generate_financial_advice(income, expenses, debts_total):
    """توليد نصائح مالية مخصصة"""
    advice = []
    
    if income == 0:
        advice.append("ابدأ بتسجيل راتبك الشهري لتتبع وضعك المالي")
        return advice
    
    expense_ratio = (expenses / income) * 100 if income > 0 else 0
    debt_ratio = (debts_total / income) * 100 if income > 0 else 0
    savings_ratio = ((income - expenses) / income) * 100 if income > 0 else 0
    
    # نصائح حسب نسبة الإنفاق
    if expense_ratio > 90:
        advice.append("⚠️ نسبة إنفاقك عالية جداً! حاول تقليل المصروفات غير الضرورية")
    elif expense_ratio > 80:
        advice.append("⚡ نسبة إنفاقك مرتفعة، راجع ميزانيتك وحدد أولوياتك")
    elif expense_ratio > 60:
        advice.append("👍 نسبة إنفاق معقولة، حاول زيادة التوفير")
    else:
        advice.append("🌟 أحسنت! نسبة إنفاق ممتازة")
    
    # نصائح حسب نسبة الديون
    if debt_ratio > 40:
        advice.append("🚨 نسبة الديون عالية، ركز على سدادها أولاً")
    elif debt_ratio > 20:
        advice.append("⚠️ راقب ديونك وضع خطة للسداد")
    elif debt_ratio > 0:
        advice.append("💪 ديونك تحت السيطرة، استمر في السداد")
    
    # نصائح حسب نسبة التوفير
    if savings_ratio < 10:
        advice.append("💰 حاول توفير 10-20% من راتبك على الأقل")
    elif savings_ratio < 20:
        advice.append("📈 توفيرك جيد، حاول زيادته إلى 20%")
    else:
        advice.append("🎯 ممتاز! معدل توفير رائع")
    
    # نصائح عامة
    if len(advice) < 3:
        general_tips = [
            "📊 راجع مصروفاتك أسبوعياً",
            "🎯 ضع أهدافاً مالية واضحة",
            "📚 استثمر في تطوير مهاراتك",
            "🛡️ أنشئ صندوق طوارئ",
            "📝 استخدم قائمة التسوق"
        ]
        advice.extend(general_tips[:3-len(advice)])
    
    return advice

def calculate_monthly_summary(salaries, expenses, debts):
    """حساب ملخص شهري"""
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    # إجمالي الرواتب هذا الشهر
    monthly_income = sum(
        salary.total_salary for salary in salaries 
        if salary.month == current_month and salary.year == current_year
    )
    
    # إجمالي المصروفات هذا الشهر
    monthly_expenses = sum(
        expense.amount for expense in expenses
        if expense.expense_date.month == current_month and expense.expense_date.year == current_year
    )
    
    # إجمالي الديون النشطة
    total_debts = sum(debt.remaining_amount for debt in debts if debt.status == 'active')
    
    # الرصيد
    balance = monthly_income - monthly_expenses
    
    # النسب
    expense_ratio = calculate_percentage(monthly_expenses, monthly_income)
    debt_ratio = calculate_percentage(total_debts, monthly_income)
    savings_ratio = calculate_percentage(balance, monthly_income)
    
    return {
        'income': monthly_income,
        'expenses': monthly_expenses,
        'debts': total_debts,
        'balance': balance,
        'expense_ratio': expense_ratio,
        'debt_ratio': debt_ratio,
        'savings_ratio': savings_ratio,
        'status': get_financial_status(monthly_income, monthly_expenses),
        'advice': generate_financial_advice(monthly_income, monthly_expenses, total_debts)
    }

def validate_amount(amount_str):
    """التحقق من صحة المبلغ المدخل"""
    try:
        # إزالة الفواصل والمسافات
        cleaned = amount_str.replace(',', '').replace(' ', '')
        amount = float(cleaned)
        
        if amount < 0:
            return False, "المبلغ لا يمكن أن يكون سالباً"
        
        if amount > 999999999:
            return False, "المبلغ كبير جداً"
        
        return True, amount
    except ValueError:
        return False, "يرجى إدخال رقم صحيح"

def get_chart_data_for_expenses(expenses):
    """تحضير بيانات الرسم البياني للمصروفات"""
    # تجميع المصروفات حسب الفئة
    categories = {}
    for expense in expenses:
        if expense.category in categories:
            categories[expense.category] += expense.amount
        else:
            categories[expense.category] = expense.amount
    
    # ترتيب حسب المبلغ
    sorted_categories = sorted(categories.items(), key=lambda x: x[1], reverse=True)
    
    return {
        'labels': [item[0] for item in sorted_categories],
        'data': [item[1] for item in sorted_categories],
        'colors': [AppSettings.CHART_COLORS[i % len(AppSettings.CHART_COLORS)] 
                  for i in range(len(sorted_categories))]
    }

def format_number_arabic(number):
    """تنسيق الأرقام بالطريقة العربية"""
    try:
        return f"{number:,.0f}".replace(',', '٬')
    except:
        return "0"

def get_current_month_name():
    """الحصول على اسم الشهر الحالي بالعربية"""
    current_month = datetime.now().month
    return get_arabic_month_name(current_month)

def calculate_days_until_due(due_date):
    """حساب الأيام المتبقية حتى تاريخ الاستحقاق"""
    if not due_date:
        return None
    
    today = date.today()
    if isinstance(due_date, str):
        due_date = datetime.strptime(due_date, '%Y-%m-%d').date()
    
    return (due_date - today).days

def get_expense_trend(expenses, months=6):
    """الحصول على اتجاه المصروفات للأشهر الماضية"""
    from collections import defaultdict
    
    monthly_expenses = defaultdict(float)
    
    for expense in expenses:
        month_key = f"{expense.expense_date.year}-{expense.expense_date.month:02d}"
        monthly_expenses[month_key] += expense.amount
    
    # ترتيب حسب التاريخ
    sorted_months = sorted(monthly_expenses.items())
    
    return {
        'months': [item[0] for item in sorted_months[-months:]],
        'amounts': [item[1] for item in sorted_months[-months:]]
    }
