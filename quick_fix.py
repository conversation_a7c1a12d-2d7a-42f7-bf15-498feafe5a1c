#!/usr/bin/env python3
"""
إصلاح سريع لمشكلة النماذج
Quick Fix for Forms Issue
"""

from app import app, db, Salary, Expense, Debt
from datetime import datetime, date

def test_direct_creation():
    """اختبار إنشاء البيانات مباشرة"""
    print("🧪 اختبار إنشاء البيانات مباشرة...")
    
    with app.app_context():
        try:
            # إنشاء راتب تجريبي
            test_salary = Salary(
                first_salary=2000000,
                second_salary=500000,
                total_salary=2500000,
                month=datetime.now().month,
                year=datetime.now().year,
                notes="راتب تجريبي"
            )
            db.session.add(test_salary)
            
            # إنشاء مصروف تجريبي
            test_expense = Expense(
                amount=300000,
                category="طعام",
                description="تسوق شهري",
                expense_date=date.today()
            )
            db.session.add(test_expense)
            
            # إنشاء دين تجريبي
            test_debt = Debt(
                creditor_name="بنك بغداد",
                debt_type="سيارة",
                total_amount=25000000,
                monthly_payment=1250000,
                remaining_amount=25000000,
                installments_count=20,
                monthly_due_date=15,
                description="قرض سيارة"
            )
            db.session.add(test_debt)
            
            # حفظ جميع البيانات
            db.session.commit()
            
            print("✅ تم إنشاء البيانات التجريبية بنجاح!")
            print(f"   - راتب: {test_salary.total_salary:,.0f} د.ع")
            print(f"   - مصروف: {test_expense.amount:,.0f} د.ع ({test_expense.category})")
            print(f"   - دين: {test_debt.creditor_name} - {test_debt.total_amount:,.0f} د.ع")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات: {e}")
            db.session.rollback()
            return False

def check_existing_data():
    """فحص البيانات الموجودة"""
    print("🔍 فحص البيانات الموجودة...")
    
    with app.app_context():
        try:
            # عد الرواتب
            salary_count = Salary.query.count()
            print(f"📊 عدد الرواتب: {salary_count}")
            
            # عد المصروفات
            expense_count = Expense.query.count()
            print(f"📊 عدد المصروفات: {expense_count}")
            
            # عد الديون
            debt_count = Debt.query.count()
            print(f"📊 عدد الديون: {debt_count}")
            
            if salary_count > 0:
                latest_salary = Salary.query.order_by(Salary.date_added.desc()).first()
                print(f"   آخر راتب: {latest_salary.total_salary:,.0f} د.ع ({latest_salary.month}/{latest_salary.year})")
            
            if expense_count > 0:
                latest_expense = Expense.query.order_by(Expense.date_added.desc()).first()
                print(f"   آخر مصروف: {latest_expense.amount:,.0f} د.ع ({latest_expense.category})")
            
            if debt_count > 0:
                latest_debt = Debt.query.order_by(Debt.date_added.desc()).first()
                print(f"   آخر دين: {latest_debt.creditor_name} - {latest_debt.total_amount:,.0f} د.ع")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص البيانات: {e}")
            return False

def disable_csrf_temporarily():
    """تعطيل CSRF مؤقتاً للاختبار"""
    print("🔧 تعطيل CSRF مؤقتاً...")
    
    try:
        # إنشاء ملف تكوين مؤقت
        config_content = """
# تعطيل CSRF مؤقتاً للاختبار
WTF_CSRF_ENABLED = False
"""
        
        with open('temp_config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print("✅ تم إنشاء ملف تكوين مؤقت")
        print("💡 أضف هذا السطر في app.py:")
        print("   app.config['WTF_CSRF_ENABLED'] = False")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التكوين: {e}")
        return False

def create_simple_test_form():
    """إنشاء نموذج اختبار بسيط"""
    print("📝 إنشاء نموذج اختبار بسيط...")
    
    test_form_html = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النماذج</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>اختبار إضافة راتب</h2>
        <form method="POST" action="/test_salary">
            <div class="mb-3">
                <label class="form-label">الراتب الأول</label>
                <input type="number" name="first_salary" class="form-control" required>
            </div>
            <div class="mb-3">
                <label class="form-label">الراتب الثاني</label>
                <input type="number" name="second_salary" class="form-control">
            </div>
            <div class="mb-3">
                <label class="form-label">الشهر</label>
                <select name="month" class="form-select" required>
                    <option value="1">يناير</option>
                    <option value="2">فبراير</option>
                    <option value="3">مارس</option>
                    <option value="4">أبريل</option>
                    <option value="5">مايو</option>
                    <option value="6">يونيو</option>
                    <option value="7">يوليو</option>
                    <option value="8">أغسطس</option>
                    <option value="9">سبتمبر</option>
                    <option value="10">أكتوبر</option>
                    <option value="11">نوفمبر</option>
                    <option value="12">ديسمبر</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">السنة</label>
                <input type="number" name="year" class="form-control" value="2024" required>
            </div>
            <button type="submit" class="btn btn-primary">حفظ الراتب</button>
        </form>
    </div>
</body>
</html>
"""
    
    try:
        with open('templates/test_form.html', 'w', encoding='utf-8') as f:
            f.write(test_form_html)
        
        print("✅ تم إنشاء نموذج اختبار بسيط")
        print("🌐 يمكنك الوصول إليه على: http://localhost:5000/test_form")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النموذج: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔧 إصلاح سريع لمشكلة النماذج")
    print("=" * 60)
    print()
    
    # فحص البيانات الموجودة
    check_existing_data()
    print()
    
    # اختبار إنشاء البيانات مباشرة
    creation_ok = test_direct_creation()
    print()
    
    if creation_ok:
        print("✅ قاعدة البيانات تعمل بشكل مثالي!")
        print("💡 المشكلة في النماذج أو CSRF")
        print()
        
        # إنشاء حلول مؤقتة
        disable_csrf_temporarily()
        print()
        
        create_simple_test_form()
        print()
        
        print("🎯 الحلول المقترحة:")
        print("1. أضف app.config['WTF_CSRF_ENABLED'] = False في app.py")
        print("2. تأكد من وجود {{ form.hidden_tag() }} في جميع النماذج")
        print("3. تحقق من أن أسماء الحقول في HTML تطابق أسماء الحقول في النماذج")
        print("4. استخدم نموذج الاختبار البسيط للتأكد من عمل النظام")
        
    else:
        print("❌ مشكلة في قاعدة البيانات")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
