{% extends "base.html" %}

{% block title %}لوحة المعلومات - مصروف{% endblock %}

{% block content %}
<div class="row">
    <!-- الإحصائيات السريعة -->
    <div class="col-12 mb-4">
        <h2 class="text-white mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            لوحة المعلومات
        </h2>
    </div>
    
    <!-- بطاقات الإحصائيات -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">آخر راتب</h6>
                    <h3 class="mb-0">
                        {% if latest_salary %}
                            {{ "{:,.0f}".format(latest_salary.total_salary) }} د.ع
                        {% else %}
                            0 د.ع
                        {% endif %}
                    </h3>
                    {% if latest_salary %}
                        <small>{{ latest_salary.month }}/{{ latest_salary.year }}</small>
                    {% endif %}
                </div>
                <div class="stats-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">مصروفات الشهر</h6>
                    <h3 class="mb-0">{{ "{:,.0f}".format(total_expenses_month) }} د.ع</h3>
                    <small>{{ "الشهر الحالي" }}</small>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">الديون النشطة</h6>
                    <h3 class="mb-0">{{ active_debts }}</h3>
                    <small>{{ "{:,.0f}".format(total_debt_amount) }} د.ع</small>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">الرصيد المتوقع</h6>
                    <h3 class="mb-0">
                        {% set balance = (latest_salary.total_salary if latest_salary else 0) - total_expenses_month %}
                        {{ "{:,.0f}".format(balance) }} د.ع
                    </h3>
                    <small>{{ "تقديري" }}</small>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-piggy-bank"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الإجراءات السريعة -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2 text-primary"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ url_for('salary') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة راتب جديد
                    </a>
                    <a href="{{ url_for('expenses') }}" class="btn btn-success">
                        <i class="fas fa-shopping-cart me-2"></i>
                        تسجيل مصروف
                    </a>
                    <a href="{{ url_for('debts') }}" class="btn btn-warning">
                        <i class="fas fa-credit-card me-2"></i>
                        إضافة دين
                    </a>
                    <a href="{{ url_for('reports') }}" class="btn btn-info">
                        <i class="fas fa-chart-bar me-2"></i>
                        عرض التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- آخر المعاملات -->
    <div class="col-lg-8 mb-4">
        <div class="card h-100">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2 text-primary"></i>
                    آخر المصروفات
                </h5>
            </div>
            <div class="card-body">
                {% if recent_expenses %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الوصف</th>
                                    <th>الفئة</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for expense in recent_expenses %}
                                <tr>
                                    <td>{{ expense.description }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ expense.category }}</span>
                                    </td>
                                    <td class="text-danger fw-bold">{{ "{:,.0f}".format(expense.amount) }} د.ع</td>
                                    <td class="text-muted">{{ expense.expense_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('expenses') }}" class="btn btn-outline-primary">
                            عرض جميع المصروفات
                            <i class="fas fa-arrow-left me-2"></i>
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد مصروفات مسجلة بعد</h6>
                        <p class="text-muted">ابدأ بتسجيل مصروفاتك لتتبع إنفاقك</p>
                        <a href="{{ url_for('expenses') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مصروف
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">
                    <i class="fas fa-info-circle me-2 text-info"></i>
                    مرحباً بك في تطبيق مصروف
                </h5>
                <p class="card-text text-muted">
                    تطبيق شامل لإدارة رواتبك ومصروفاتك وديونك الشخصية بطريقة سهلة ومنظمة
                </p>
                <div class="row text-center mt-4">
                    <div class="col-md-3">
                        <i class="fas fa-money-bill-wave fa-2x text-primary mb-2"></i>
                        <h6>إدارة الرواتب</h6>
                        <small class="text-muted">تتبع رواتبك الشهرية</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-shopping-cart fa-2x text-success mb-2"></i>
                        <h6>تسجيل المصروفات</h6>
                        <small class="text-muted">راقب إنفاقك اليومي</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-credit-card fa-2x text-warning mb-2"></i>
                        <h6>إدارة الديون</h6>
                        <small class="text-muted">تتبع التزاماتك المالية</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-chart-bar fa-2x text-info mb-2"></i>
                        <h6>التقارير والإحصائيات</h6>
                        <small class="text-muted">تحليل مالي شامل</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
