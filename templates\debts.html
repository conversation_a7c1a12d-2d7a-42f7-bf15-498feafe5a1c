{% extends "base.html" %}

{% block title %}إدارة الديون - نظام إدارة الرواتب{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-credit-card me-3"></i>
            إدارة الديون والأقساط
        </h1>
    </div>
</div>

<!-- إحصائيات الديون -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-list fa-2x text-primary mb-3"></i>
                <h4 class="text-primary">{{ total_debts }}</h4>
                <p class="text-muted mb-0">إجمالي الديون النشطة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                <h4 class="text-danger">{{ total_debt_amount | currency }}</h4>
                <p class="text-muted mb-0">إجمالي المبلغ المتبقي</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-money-bill-wave fa-2x text-warning mb-3"></i>
                <h4 class="text-warning">{{ total_original_amount | currency }}</h4>
                <p class="text-muted mb-0">إجمالي المبلغ الأصلي</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                <h4 class="text-success">{{ total_paid | currency }}</h4>
                <p class="text-muted mb-0">إجمالي المبلغ المدفوع</p>
            </div>
        </div>
    </div>
</div>

<!-- أزرار العمليات -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-credit-card me-2 text-primary"></i>
                        إدارة الديون
                    </h5>
                    <div>
                        <a href="{{ url_for('add_debt') }}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>
                            إضافة دين جديد
                        </a>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-home me-2"></i>
                            العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الديون -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    قائمة الديون ({{ debts|length }} دين)
                </h5>
            </div>
            <div class="card-body">
                {% if debts %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>الدائن/الجهة</th>
                                    <th>نوع الدين</th>
                                    <th>المبلغ الأصلي</th>
                                    <th>المبلغ المتبقي</th>
                                    <th>القسط الشهري</th>
                                    <th>نسبة السداد</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for debt in debts %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <strong>{{ debt.creditor_name }}</strong>
                                        {% if debt.description %}
                                            <br><small class="text-muted">{{ debt.description[:30] }}{% if debt.description|length > 30 %}...{% endif %}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ debt.debt_type }}</span>
                                    </td>
                                    <td class="text-warning fw-bold">
                                        {{ debt.total_amount | currency }}
                                    </td>
                                    <td class="text-danger fw-bold">
                                        {{ debt.remaining_amount | currency }}
                                    </td>
                                    <td class="text-info">
                                        {% if debt.monthly_payment %}
                                            {{ debt.monthly_payment | currency }}
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar 
                                                {% if debt.progress_percentage >= 75 %}bg-success
                                                {% elif debt.progress_percentage >= 50 %}bg-warning
                                                {% elif debt.progress_percentage >= 25 %}bg-info
                                                {% else %}bg-danger{% endif %}" 
                                                role="progressbar" 
                                                style="width: {{ debt.progress_percentage }}%">
                                                {{ "%.1f"|format(debt.progress_percentage) }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-muted">
                                        {{ debt.date_added.strftime('%Y-%m-%d') }}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('debt_details', debt_id=debt.id) }}"
                                               class="btn btn-outline-primary" title="التفاصيل والدفعات">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('edit_debt', debt_id=debt.id) }}"
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger"
                                                    title="حذف" onclick="confirmDelete({{ debt.id }}, '{{ debt.creditor_name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- إحصائيات تفصيلية -->
                    {% if total_original_amount > 0 %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                                        نسبة السداد الإجمالية
                                    </h6>
                                    <div class="progress mb-3" style="height: 30px;">
                                        {% set overall_progress = (total_paid / total_original_amount) * 100 %}
                                        <div class="progress-bar 
                                            {% if overall_progress >= 75 %}bg-success
                                            {% elif overall_progress >= 50 %}bg-warning
                                            {% elif overall_progress >= 25 %}bg-info
                                            {% else %}bg-danger{% endif %}" 
                                            role="progressbar" 
                                            style="width: {{ overall_progress }}%">
                                            {{ "%.1f"|format(overall_progress) }}% مكتمل
                                        </div>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col-md-4">
                                            <h6 class="text-muted">المدفوع</h6>
                                            <h5 class="text-success">{{ total_paid | currency }}</h5>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-muted">المتبقي</h6>
                                            <h5 class="text-danger">{{ total_debt_amount | currency }}</h5>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-muted">الإجمالي</h6>
                                            <h5 class="text-primary">{{ total_original_amount | currency }}</h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-credit-card fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted">لا توجد ديون مسجلة</h4>
                        <p class="text-muted mb-4">ابدأ بإضافة دين جديد لتتمكن من تتبع التزاماتك المالية</p>
                        <a href="{{ url_for('add_debt') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            إضافة دين جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
{% if debts %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح لإدارة الديون
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calendar-check text-success me-2 mt-1"></i>
                            <div>
                                <strong>انتظام السداد</strong>
                                <p class="text-muted small mb-0">التزم بمواعيد السداد لتجنب الفوائد الإضافية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-chart-line text-info me-2 mt-1"></i>
                            <div>
                                <strong>راقب التقدم</strong>
                                <p class="text-muted small mb-0">تابع نسبة السداد لكل دين بانتظام</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-piggy-bank text-warning me-2 mt-1"></i>
                            <div>
                                <strong>خطط للسداد</strong>
                                <p class="text-muted small mb-0">ضع خطة واضحة لسداد الديون حسب الأولوية</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(debtId, creditorName) {
        if (confirm(`هل أنت متأكد من حذف دين "${creditorName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // إنشاء نموذج مخفي لإرسال طلب الحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/delete_debt/${debtId}`;
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    // تحسين عرض الجدول على الأجهزة الصغيرة
    document.addEventListener('DOMContentLoaded', function() {
        if (window.innerWidth < 768) {
            const table = document.querySelector('.table');
            if (table) {
                table.style.fontSize = '0.85rem';
            }
        }
    });
</script>
{% endblock %}
