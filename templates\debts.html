{% extends "base.html" %}

{% block title %}إدارة الديون - مصروف{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <h2 class="text-white mb-4">
            <i class="fas fa-credit-card me-2"></i>
            إدارة الديون
        </h2>
    </div>
</div>

<div class="row">
    <!-- نموذج إضافة دين جديد -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة دين/قرض جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.creditor_name.label(class="form-label") }}
                        {{ form.creditor_name(class="form-control", placeholder="اسم البنك أو الشخص أو الجهة") }}
                        {% if form.creditor_name.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.creditor_name.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.debt_type.label(class="form-label") }}
                        {{ form.debt_type(class="form-select") }}
                        {% if form.debt_type.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.debt_type.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.total_amount.label(class="form-label") }}
                        {{ form.total_amount(class="form-control", placeholder="أدخل المبلغ الإجمالي للدين") }}
                        {% if form.total_amount.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.total_amount.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.monthly_payment.label(class="form-label") }}
                            {{ form.monthly_payment(class="form-control", placeholder="القسط الشهري") }}
                            <small class="text-muted">اختياري</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.installments_count.label(class="form-label") }}
                            {{ form.installments_count(class="form-control", placeholder="عدد الأقساط") }}
                            <small class="text-muted">اختياري</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.monthly_due_date.label(class="form-label") }}
                        {{ form.monthly_due_date(class="form-control", placeholder="مثال: 15 (لليوم 15 من كل شهر)") }}
                        <small class="text-muted">اختياري - يوم الاستحقاق الشهري</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.start_date.label(class="form-label") }}
                            {{ form.start_date(class="form-control", type="date") }}
                            <small class="text-muted">اختياري</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.end_date.label(class="form-label") }}
                            {{ form.end_date(class="form-control", type="date") }}
                            <small class="text-muted">اختياري</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3", placeholder="تفاصيل إضافية عن الدين...") }}
                        {% if form.description.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.description.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-2"></i>
                            حفظ الدين
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- إحصائيات الديون -->
        <div class="card mt-4">
            <div class="card-header bg-danger text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات الديون
                </h6>
            </div>
            <div class="card-body">
                {% set total_debts = debts|sum(attribute='remaining_amount') %}
                {% set debts_count = debts|length %}
                
                <div class="text-center mb-3">
                    <h4 class="text-danger">{{ "{:,.0f}".format(total_debts) }} د.ع</h4>
                    <small class="text-muted">إجمالي الديون المتبقية</small>
                </div>
                
                <div class="text-center">
                    <h5 class="text-warning">{{ debts_count }}</h5>
                    <small class="text-muted">عدد الديون النشطة</small>
                </div>
                
                {% if debts %}
                    <hr>
                    <div class="text-center">
                        <h6 class="text-info">{{ "{:,.0f}".format(total_debts / debts_count) }} د.ع</h6>
                        <small class="text-muted">متوسط الدين</small>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- قائمة الديون -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2 text-primary"></i>
                    الديون النشطة
                </h5>
            </div>
            <div class="card-body">
                {% if debts %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الدائن/الجهة</th>
                                    <th>نوع الدين</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>المبلغ المتبقي</th>
                                    <th>القسط الشهري</th>
                                    <th>الأقساط</th>
                                    <th>يوم الاستحقاق</th>
                                    <th>الحالة</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for debt in debts %}
                                <tr>
                                    <td>
                                        <strong>{{ debt.creditor_name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ debt.debt_type }}</span>
                                    </td>
                                    <td class="text-info fw-bold">
                                        {{ "{:,.0f}".format(debt.total_amount) }} د.ع
                                    </td>
                                    <td class="text-danger fw-bold">
                                        {{ "{:,.0f}".format(debt.remaining_amount) }} د.ع
                                    </td>
                                    <td class="text-warning fw-bold">
                                        {% if debt.monthly_payment %}
                                            {{ "{:,.0f}".format(debt.monthly_payment) }} د.ع
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        {% if debt.installments_count %}
                                            <span class="badge bg-primary">
                                                {{ debt.paid_installments }}/{{ debt.installments_count }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        {% if debt.monthly_due_date %}
                                            <span class="badge bg-info">{{ debt.monthly_due_date }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if debt.remaining_amount == debt.total_amount %}
                                            <span class="badge bg-danger">لم يُسدد</span>
                                        {% elif debt.remaining_amount > 0 %}
                                            <span class="badge bg-warning">مُسدد جزئياً</span>
                                        {% else %}
                                            <span class="badge bg-success">مُسدد بالكامل</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('debt_details', debt_id=debt.id) }}"
                                               class="btn btn-outline-primary" title="التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('debt_payment') }}?debt_id={{ debt.id }}"
                                               class="btn btn-outline-success" title="إضافة دفعة">
                                                <i class="fas fa-plus"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- تحليل الديون -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted mb-1">الديون المستحقة خلال أسبوع</h6>
                                    <h4 class="text-warning mb-0">0</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted mb-1">الديون المتأخرة</h6>
                                    <h4 class="text-danger mb-0">0</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد ديون مسجلة</h6>
                        <p class="text-muted">هذا أمر جيد! إذا كان لديك ديون، يمكنك إضافتها من النموذج على اليسار</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نصائح لإدارة الديون -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح لإدارة الديون
                </h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calendar-check text-success me-2 mt-1"></i>
                            <div>
                                <strong>حدد مواعيد الاستحقاق</strong>
                                <p class="text-muted small mb-0">سجل تواريخ استحقاق الديون لتجنب التأخير</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-sort-amount-up text-info me-2 mt-1"></i>
                            <div>
                                <strong>رتب حسب الأولوية</strong>
                                <p class="text-muted small mb-0">ابدأ بسداد الديون ذات الفوائد العالية أولاً</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-coins text-warning me-2 mt-1"></i>
                            <div>
                                <strong>سدد بانتظام</strong>
                                <p class="text-muted small mb-0">حتى لو كانت مبالغ صغيرة، السداد المنتظم مهم</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-handshake text-primary me-2 mt-1"></i>
                            <div>
                                <strong>تواصل مع الدائنين</strong>
                                <p class="text-muted small mb-0">في حالة صعوبة السداد، تواصل لإعادة الجدولة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // إضافة moment.js للتعامل مع التواريخ
    document.addEventListener('DOMContentLoaded', function() {
        // يمكن إضافة JavaScript إضافي هنا للتعامل مع التواريخ
    });
</script>
{% endblock %}
