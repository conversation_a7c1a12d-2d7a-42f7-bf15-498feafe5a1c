#!/usr/bin/env python3
"""
تطبيق مصروف - إدارة الرواتب والمصروفات الشخصية
Masroof - Personal Finance Management Application

تطبيق ويب متكامل لإدارة الأموال الشخصية باللغة العربية
"""

import os
import sys
from app import app

def main():
    """تشغيل التطبيق"""
    print("=" * 60)
    print("🚀 تطبيق مصروف - إدارة الرواتب والمصروفات الشخصية")
    print("=" * 60)
    print()
    print("📋 معلومات التطبيق:")
    print("   • اللغة: العربية")
    print("   • التقنية: Flask + Bootstrap 5")
    print("   • قاعدة البيانات: SQLite")
    print("   • الواجهة: متجاوبة وحديثة")
    print()
    print("🌐 روابط مهمة:")
    print("   • الصفحة الرئيسية: http://localhost:5000")
    print("   • إدارة الرواتب: http://localhost:5000/salary")
    print("   • إدارة المصروفات: http://localhost:5000/expenses")
    print("   • إدارة الديون: http://localhost:5000/debts")
    print("   • التقارير: http://localhost:5000/reports")
    print()
    print("⚡ الميزات المتوفرة:")
    print("   ✅ إدخال وتتبع الرواتب الشهرية")
    print("   ✅ تسجيل المصروفات حسب الفئات")
    print("   ✅ إدارة الديون والالتزامات")
    print("   ✅ تقارير وإحصائيات تفاعلية")
    print("   ✅ واجهة عربية متجاوبة")
    print("   ✅ رسوم بيانية تفاعلية")
    print()
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("=" * 60)
    print()
    
    try:
        # تشغيل التطبيق
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n")
        print("=" * 60)
        print("🛑 تم إيقاف التطبيق بنجاح")
        print("شكراً لاستخدام تطبيق مصروف!")
        print("=" * 60)
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
