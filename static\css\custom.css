/* تحسينات إضافية للتطبيق */

/* تحسين الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
    direction: rtl;
    text-align: right;
}

/* تحسين الأزرار */
.btn {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* تحسين البطاقات */
.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

/* تحسين النماذج */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: scale(1.02);
}

/* تحسين الجداول */
.table {
    font-family: 'Cairo', sans-serif;
    border-radius: 12px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    border: none;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* تحسين الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
}

/* تحسين التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
}

/* تحسين الشريط العلوي */
.navbar {
    backdrop-filter: blur(15px);
    background: rgba(255, 255, 255, 0.95) !important;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    font-size: 1.8rem;
}

/* تحسين الأيقونات */
.fas, .far {
    margin-left: 8px;
}

/* تحسين الألوان */
.text-primary { color: #667eea !important; }
.text-success { color: #28a745 !important; }
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #17a2b8 !important; }

/* تحسين الخلفية */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* تحسين الرسوم البيانية */
canvas {
    border-radius: 10px;
}

/* تحسين التصميم المتجاوب */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .stats-card {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        border-radius: 10px;
    }
}

/* تحسين الأزرار الخاصة */
.btn-gradient-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    color: white;
}

.btn-gradient-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
}

.btn-gradient-danger {
    background: linear-gradient(45deg, #dc3545, #e83e8c);
    border: none;
    color: white;
}

.btn-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    border: none;
    color: white;
}

/* تحسين الشارات */
.badge {
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    border-radius: 6px;
}

/* تحسين التقسيم */
.pagination .page-link {
    font-family: 'Cairo', sans-serif;
    border-radius: 6px;
    margin: 0 2px;
    border: none;
    color: #667eea;
}

.pagination .page-link:hover {
    background-color: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* تحسين التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    border-top-color: #667eea;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسين الظلال */
.shadow-custom {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
}

.shadow-hover:hover {
    box-shadow: 0 15px 40px rgba(0,0,0,0.15) !important;
}

/* تحسين النصوص */
.text-gradient {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}
