{% extends "base.html" %}

{% block title %}تفاصيل الدين - {{ debt.creditor_name }} - مصروف{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <h2 class="text-white mb-4">
            <i class="fas fa-file-invoice-dollar me-2"></i>
            تفاصيل الدين - {{ debt.creditor_name }}
        </h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}" class="text-white">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('debts') }}" class="text-white">الديون</a></li>
                <li class="breadcrumb-item active text-white">{{ debt.creditor_name }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <!-- معلومات الدين الأساسية -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدين
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label text-muted">الدائن/الجهة:</label>
                    <h6>{{ debt.creditor_name }}</h6>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted">نوع الدين:</label>
                    <span class="badge bg-secondary fs-6">{{ debt.debt_type }}</span>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted">المبلغ الإجمالي:</label>
                    <h5 class="text-info">{{ "{:,.0f}".format(debt.total_amount) }} د.ع</h5>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted">المبلغ المتبقي:</label>
                    <h5 class="text-danger">{{ "{:,.0f}".format(debt.remaining_amount) }} د.ع</h5>
                </div>
                
                {% if debt.monthly_payment %}
                <div class="mb-3">
                    <label class="form-label text-muted">القسط الشهري:</label>
                    <h6 class="text-warning">{{ "{:,.0f}".format(debt.monthly_payment) }} د.ع</h6>
                </div>
                {% endif %}
                
                {% if debt.installments_count %}
                <div class="mb-3">
                    <label class="form-label text-muted">الأقساط:</label>
                    <span class="badge bg-primary fs-6">{{ debt.paid_installments }}/{{ debt.installments_count }}</span>
                </div>
                {% endif %}
                
                {% if debt.monthly_due_date %}
                <div class="mb-3">
                    <label class="form-label text-muted">يوم الاستحقاق الشهري:</label>
                    <span class="badge bg-info fs-6">{{ debt.monthly_due_date }}</span>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <label class="form-label text-muted">الحالة:</label>
                    {% if debt.remaining_amount == debt.total_amount %}
                        <span class="badge bg-danger fs-6">لم يُسدد</span>
                    {% elif debt.remaining_amount > 0 %}
                        <span class="badge bg-warning fs-6">مُسدد جزئياً</span>
                    {% else %}
                        <span class="badge bg-success fs-6">مُسدد بالكامل</span>
                    {% endif %}
                </div>
                
                {% if debt.description %}
                <div class="mb-3">
                    <label class="form-label text-muted">الوصف:</label>
                    <p class="text-muted">{{ debt.description }}</p>
                </div>
                {% endif %}
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('debt_payment') }}?debt_id={{ debt.id }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دفعة جديدة
                    </a>
                    <a href="{{ url_for('debts') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للديون
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- شريط التقدم والإحصائيات -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2 text-primary"></i>
                    تقدم السداد
                </h5>
            </div>
            <div class="card-body">
                <!-- شريط التقدم -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between mb-2">
                        <span>نسبة السداد</span>
                        <span class="fw-bold">{{ "{:.1f}".format(progress_percentage) }}%</span>
                    </div>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ progress_percentage }}%" 
                             aria-valuenow="{{ progress_percentage }}" 
                             aria-valuemin="0" aria-valuemax="100">
                        </div>
                    </div>
                </div>
                
                <!-- إحصائيات سريعة -->
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-success">{{ "{:,.0f}".format(total_paid) }}</h4>
                            <small class="text-muted">المبلغ المدفوع (د.ع)</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-danger">{{ "{:,.0f}".format(debt.remaining_amount) }}</h4>
                            <small class="text-muted">المبلغ المتبقي (د.ع)</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-info">{{ payments|length }}</h4>
                            <small class="text-muted">عدد الدفعات</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            {% if payments %}
                                <h4 class="text-warning">{{ "{:,.0f}".format(total_paid / payments|length) }}</h4>
                                <small class="text-muted">متوسط الدفعة (د.ع)</small>
                            {% else %}
                                <h4 class="text-muted">0</h4>
                                <small class="text-muted">متوسط الدفعة (د.ع)</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- سجل الدفعات -->
        <div class="card">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2 text-primary"></i>
                    سجل الدفعات
                </h5>
            </div>
            <div class="card-body">
                {% if payments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>نوع الدفعة</th>
                                    <th>رقم الإيصال</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ payment.payment_date.strftime('%Y-%m-%d') }}
                                        </span>
                                    </td>
                                    <td class="text-success fw-bold">
                                        {{ "{:,.0f}".format(payment.amount) }} د.ع
                                    </td>
                                    <td>
                                        {% set payment_types = {
                                            'installment': ('قسط شهري', 'primary'),
                                            'extra': ('دفعة إضافية', 'success'),
                                            'early': ('سداد مبكر', 'warning'),
                                            'penalty': ('غرامة تأخير', 'danger')
                                        } %}
                                        {% set type_info = payment_types.get(payment.payment_type, ('أخرى', 'secondary')) %}
                                        <span class="badge bg-{{ type_info[1] }}">{{ type_info[0] }}</span>
                                    </td>
                                    <td>
                                        {% if payment.receipt_number %}
                                            <code>{{ payment.receipt_number }}</code>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if payment.notes %}
                                            <span class="text-muted" title="{{ payment.notes }}">
                                                {{ payment.notes[:30] }}{% if payment.notes|length > 30 %}...{% endif %}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد دفعات مسجلة بعد</h6>
                        <p class="text-muted">ابدأ بتسجيل دفعتك الأولى</p>
                        <a href="{{ url_for('debt_payment') }}?debt_id={{ debt.id }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة دفعة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث شريط التقدم بتأثير متحرك
    document.addEventListener('DOMContentLoaded', function() {
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            const targetWidth = progressBar.style.width;
            progressBar.style.width = '0%';
            
            setTimeout(() => {
                progressBar.style.transition = 'width 2s ease-in-out';
                progressBar.style.width = targetWidth;
            }, 500);
        }
    });
</script>
{% endblock %}
