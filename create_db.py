#!/usr/bin/env python3
"""
إنشاء قاعدة البيانات الجديدة
Create New Database
"""

from app import app, db, Salary, Expense, Debt, DebtPayment
from datetime import datetime, date

def create_database():
    """إنشاء قاعدة البيانات والجداول"""
    print("🔄 إنشاء قاعدة البيانات...")
    
    with app.app_context():
        # إنشاء جميع الجداول
        db.create_all()
        print("✅ تم إنشاء جميع الجداول")
        
        # إضافة بيانات تجريبية
        add_sample_data()
        
        print("✅ تم إنشاء قاعدة البيانات بنجاح!")

def add_sample_data():
    """إضافة بيانات تجريبية"""
    print("📊 إضافة بيانات تجريبية...")
    
    try:
        # إضافة راتب تجريبي
        sample_salary = Salary(
            first_salary=2000000,
            second_salary=500000,
            total_salary=2500000,
            month=datetime.now().month,
            year=datetime.now().year,
            notes="راتب تجريبي"
        )
        db.session.add(sample_salary)
        
        # إضافة مصروفات تجريبية
        sample_expenses = [
            Expense(
                amount=300000, 
                category="طعام", 
                description="تسوق شهري", 
                expense_date=date.today()
            ),
            Expense(
                amount=150000, 
                category="مواصلات", 
                description="وقود السيارة", 
                expense_date=date.today()
            ),
            Expense(
                amount=200000, 
                category="فواتير", 
                description="فاتورة الكهرباء", 
                expense_date=date.today()
            )
        ]
        
        for expense in sample_expenses:
            db.session.add(expense)
        
        # إضافة دين تجريبي
        sample_debt = Debt(
            creditor_name="بنك بغداد",
            debt_type="سيارة",
            total_amount=50000000,
            monthly_payment=2000000,
            remaining_amount=50000000,
            installments_count=25,
            paid_installments=0,
            monthly_due_date=15,
            description="قرض سيارة"
        )
        db.session.add(sample_debt)
        
        # حفظ البيانات
        db.session.commit()
        print("✅ تم إضافة البيانات التجريبية")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات التجريبية: {e}")
        db.session.rollback()

if __name__ == "__main__":
    create_database()
