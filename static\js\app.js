// تحسينات JavaScript للتطبيق

document.addEventListener('DOMContentLoaded', function() {
    
    // تحسين تجربة المستخدم
    initializeApp();
    
    // تحسين النماذج
    enhanceForms();
    
    // تحسين الجداول
    enhanceTables();
    
    // تحسين الإحصائيات
    enhanceStats();
    
    // تحسين التنبيهات
    enhanceAlerts();
});

function initializeApp() {
    // إضافة تأثيرات التحميل
    showLoadingEffect();
    
    // تحسين التنقل
    enhanceNavigation();
    
    // تحسين الأزرار
    enhanceButtons();
    
    // إخفاء تأثير التحميل بعد التحميل
    setTimeout(hideLoadingEffect, 1000);
}

function showLoadingEffect() {
    // إضافة تأثير تحميل للصفحة
    const body = document.body;
    body.style.opacity = '0';
    body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
        body.style.opacity = '1';
    }, 100);
}

function hideLoadingEffect() {
    // إزالة تأثيرات التحميل
    const loadingElements = document.querySelectorAll('.loading');
    loadingElements.forEach(element => {
        element.style.display = 'none';
    });
}

function enhanceForms() {
    // تحسين حقول الإدخال
    const inputs = document.querySelectorAll('.form-control, .form-select');
    
    inputs.forEach(input => {
        // تأثير التركيز
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
        
        // التحقق من صحة البيانات
        input.addEventListener('input', function() {
            validateInput(this);
        });
    });
    
    // تحسين أزرار الإرسال
    const submitButtons = document.querySelectorAll('button[type="submit"]');
    submitButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!validateForm(this.closest('form'))) {
                e.preventDefault();
                showValidationErrors();
            } else {
                showSubmitLoading(this);
            }
        });
    });
}

function validateInput(input) {
    const value = input.value.trim();
    const isRequired = input.hasAttribute('required');
    
    if (isRequired && !value) {
        input.classList.add('is-invalid');
        input.classList.remove('is-valid');
    } else if (value) {
        input.classList.add('is-valid');
        input.classList.remove('is-invalid');
    } else {
        input.classList.remove('is-valid', 'is-invalid');
    }
}

function validateForm(form) {
    const requiredInputs = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredInputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        }
    });
    
    return isValid;
}

function showValidationErrors() {
    // عرض رسالة خطأ للتحقق من صحة البيانات
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger alert-dismissible fade show';
    alert.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        يرجى ملء جميع الحقول المطلوبة
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alert, container.firstChild);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        alert.remove();
    }, 5000);
}

function showSubmitLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="loading"></span> جاري الحفظ...';
    button.disabled = true;
    
    // إعادة تعيين الزر بعد 3 ثوان (في حالة عدم إعادة تحميل الصفحة)
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 3000);
}

function enhanceTables() {
    // تحسين الجداول
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        // إضافة تأثيرات التمرير
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            row.style.animationDelay = `${index * 0.1}s`;
            row.classList.add('fade-in');
        });
        
        // تحسين البحث في الجدول
        addTableSearch(table);
    });
}

function addTableSearch(table) {
    // إضافة خاصية البحث للجداول الكبيرة
    const rows = table.querySelectorAll('tbody tr');
    if (rows.length > 5) {
        const searchContainer = document.createElement('div');
        searchContainer.className = 'mb-3';
        searchContainer.innerHTML = `
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="البحث في الجدول...">
            </div>
        `;
        
        table.parentElement.insertBefore(searchContainer, table);
        
        const searchInput = searchContainer.querySelector('input');
        searchInput.addEventListener('input', function() {
            filterTable(table, this.value);
        });
    }
}

function filterTable(table, searchTerm) {
    const rows = table.querySelectorAll('tbody tr');
    const term = searchTerm.toLowerCase();
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(term)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function enhanceStats() {
    // تحسين بطاقات الإحصائيات
    const statsCards = document.querySelectorAll('.stats-card');
    
    statsCards.forEach((card, index) => {
        // تأثير الظهور المتدرج
        card.style.animationDelay = `${index * 0.2}s`;
        card.classList.add('slide-up');
        
        // تأثير العد التصاعدي للأرقام
        const numberElement = card.querySelector('h3, h4');
        if (numberElement) {
            animateNumber(numberElement);
        }
    });
}

function animateNumber(element) {
    const text = element.textContent;
    const number = parseFloat(text.replace(/[^\d.-]/g, ''));
    
    if (!isNaN(number)) {
        let current = 0;
        const increment = number / 50;
        const timer = setInterval(() => {
            current += increment;
            if (current >= number) {
                current = number;
                clearInterval(timer);
            }
            
            const formattedNumber = Math.floor(current).toLocaleString();
            element.textContent = text.replace(/[\d,.-]+/, formattedNumber);
        }, 30);
    }
}

function enhanceNavigation() {
    // تحسين التنقل
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // إضافة تأثير التحميل للتنقل
            if (!this.href.includes('#')) {
                showPageTransition();
            }
        });
    });
}

function showPageTransition() {
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    overlay.innerHTML = `
        <div class="text-center text-white">
            <div class="loading" style="width: 40px; height: 40px; border-width: 4px;"></div>
            <p class="mt-3">جاري التحميل...</p>
        </div>
    `;
    
    document.body.appendChild(overlay);
    
    setTimeout(() => {
        overlay.style.opacity = '1';
    }, 10);
}

function enhanceButtons() {
    // تحسين الأزرار
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // تأثير الموجة عند النقر
            createRippleEffect(this, e);
        });
    });
}

function createRippleEffect(button, event) {
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
    `;
    
    button.style.position = 'relative';
    button.style.overflow = 'hidden';
    button.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

function enhanceAlerts() {
    // تحسين التنبيهات
    const alerts = document.querySelectorAll('.alert');
    
    alerts.forEach(alert => {
        // تأثير الظهور
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        alert.style.transition = 'all 0.3s ease';
        
        setTimeout(() => {
            alert.style.opacity = '1';
            alert.style.transform = 'translateY(0)';
        }, 100);
        
        // إزالة تلقائية بعد 5 ثوان
        setTimeout(() => {
            if (alert.parentElement) {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    alert.remove();
                }, 300);
            }
        }, 5000);
    });
}

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .fade-in {
        animation: fadeIn 0.5s ease forwards;
    }
    
    .slide-up {
        animation: slideUp 0.6s ease forwards;
    }
    
    .focused {
        transform: scale(1.02);
        transition: transform 0.3s ease;
    }
`;

document.head.appendChild(style);
