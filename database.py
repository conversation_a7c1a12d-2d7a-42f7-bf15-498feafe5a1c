import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path="masroof.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول الأساسية"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول الرواتب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS salaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                first_salary REAL NOT NULL,
                second_salary REAL DEFAULT 0,
                total_salary REAL NOT NULL,
                month INTEGER NOT NULL,
                year INTEGER NOT NULL,
                date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT
            )
        ''')
        
        # جدول المصروفات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                amount REAL NOT NULL,
                category TEXT NOT NULL,
                description TEXT,
                date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expense_date DATE NOT NULL
            )
        ''')
        
        # جدول الديون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS debts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                creditor_name TEXT NOT NULL,
                amount REAL NOT NULL,
                remaining_amount REAL NOT NULL,
                due_date DATE,
                description TEXT,
                date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'active'
            )
        ''')
        
        # جدول دفعات الديون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS debt_payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                debt_id INTEGER NOT NULL,
                amount REAL NOT NULL,
                payment_date DATE NOT NULL,
                notes TEXT,
                FOREIGN KEY (debt_id) REFERENCES debts (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_salary(self, first_salary, second_salary=0, month=None, year=None, notes=""):
        """إضافة راتب جديد"""
        if month is None:
            month = datetime.now().month
        if year is None:
            year = datetime.now().year
            
        total_salary = first_salary + second_salary
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO salaries (first_salary, second_salary, total_salary, month, year, notes)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (first_salary, second_salary, total_salary, month, year, notes))
        
        conn.commit()
        conn.close()
        return cursor.lastrowid
    
    def get_salaries(self, year=None, month=None):
        """استرجاع الرواتب"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if year and month:
            cursor.execute('''
                SELECT * FROM salaries WHERE year = ? AND month = ?
                ORDER BY date_added DESC
            ''', (year, month))
        elif year:
            cursor.execute('''
                SELECT * FROM salaries WHERE year = ?
                ORDER BY date_added DESC
            ''', (year,))
        else:
            cursor.execute('''
                SELECT * FROM salaries ORDER BY date_added DESC
            ''')
        
        results = cursor.fetchall()
        conn.close()
        return results
    
    def get_latest_salary(self):
        """الحصول على آخر راتب مُدخل"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM salaries ORDER BY date_added DESC LIMIT 1
        ''')
        
        result = cursor.fetchone()
        conn.close()
        return result
