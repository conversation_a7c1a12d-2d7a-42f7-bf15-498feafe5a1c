#!/usr/bin/env python3
"""
ملف إصلاح الأخطاء الشائعة في تطبيق مصروف
Fix Common Errors in Masroof Application
"""

import os
import sys
from datetime import datetime

def fix_template_errors():
    """إصلاح أخطاء القوالب"""
    print("🔧 إصلاح أخطاء القوالب...")
    
    # قائمة الملفات التي تحتاج إصلاح
    template_files = [
        'templates/expenses.html',
        'templates/debts.html',
        'templates/reports.html'
    ]
    
    for file_path in template_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # إصلاح مشاكل moment()
                content = content.replace('moment().format(', 'today()')
                content = content.replace('moment().date()', 'today()')
                content = content.replace('moment()', 'today()')
                
                # إصلاح مشاكل التواريخ
                content = content.replace('value=moment().format(\'YYYY-MM-DD\')', 'value=today()')
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ تم إصلاح {file_path}")
            except Exception as e:
                print(f"❌ خطأ في إصلاح {file_path}: {e}")
        else:
            print(f"⚠️ الملف غير موجود: {file_path}")

def check_requirements():
    """التحقق من المتطلبات"""
    print("📦 التحقق من المتطلبات...")
    
    required_packages = [
        'Flask',
        'Flask-SQLAlchemy',
        'Flask-WTF',
        'WTForms'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"✅ {package} متوفر")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} غير متوفر")
    
    if missing_packages:
        print(f"\n🚨 المكتبات المفقودة: {', '.join(missing_packages)}")
        print("💡 قم بتشغيل: pip install -r requirements.txt")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def check_database():
    """التحقق من قاعدة البيانات"""
    print("🗄️ التحقق من قاعدة البيانات...")
    
    try:
        from app import app, db
        
        with app.app_context():
            # محاولة إنشاء الجداول
            db.create_all()
            print("✅ قاعدة البيانات جاهزة")
            return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def check_static_files():
    """التحقق من الملفات الثابتة"""
    print("📁 التحقق من الملفات الثابتة...")
    
    static_dirs = ['static', 'static/css', 'static/js']
    static_files = [
        'static/css/custom.css',
        'static/js/app.js'
    ]
    
    # إنشاء المجلدات إذا لم تكن موجودة
    for dir_path in static_dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            print(f"📁 تم إنشاء المجلد: {dir_path}")
    
    # التحقق من الملفات
    for file_path in static_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} موجود")
        else:
            print(f"⚠️ {file_path} غير موجود")

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    print("📊 إنشاء بيانات تجريبية...")
    
    try:
        from app import app, db, Salary, Expense, Debt
        from datetime import date
        
        with app.app_context():
            # التحقق من وجود بيانات
            if Salary.query.first():
                print("ℹ️ البيانات موجودة مسبقاً")
                return
            
            # إضافة راتب تجريبي
            sample_salary = Salary(
                first_salary=2000000,
                second_salary=500000,
                total_salary=2500000,
                month=datetime.now().month,
                year=datetime.now().year,
                notes="راتب تجريبي"
            )
            db.session.add(sample_salary)
            
            # إضافة مصروفات تجريبية
            sample_expenses = [
                Expense(amount=300000, category="طعام", description="تسوق شهري", expense_date=date.today()),
                Expense(amount=150000, category="مواصلات", description="وقود السيارة", expense_date=date.today()),
                Expense(amount=200000, category="فواتير", description="فاتورة الكهرباء", expense_date=date.today())
            ]
            
            for expense in sample_expenses:
                db.session.add(expense)
            
            # إضافة دين تجريبي
            sample_debt = Debt(
                creditor_name="أحمد محمد",
                amount=1000000,
                remaining_amount=1000000,
                description="قرض شخصي"
            )
            db.session.add(sample_debt)
            
            db.session.commit()
            print("✅ تم إنشاء البيانات التجريبية")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")

def run_diagnostics():
    """تشغيل التشخيص الشامل"""
    print("=" * 60)
    print("🔍 تشخيص تطبيق مصروف")
    print("=" * 60)
    print()
    
    # التحقق من المتطلبات
    req_ok = check_requirements()
    print()
    
    # إصلاح أخطاء القوالب
    fix_template_errors()
    print()
    
    # التحقق من الملفات الثابتة
    check_static_files()
    print()
    
    # التحقق من قاعدة البيانات
    if req_ok:
        db_ok = check_database()
        print()
        
        # إنشاء بيانات تجريبية
        if db_ok:
            create_sample_data()
            print()
    
    print("=" * 60)
    print("✅ انتهى التشخيص")
    print("=" * 60)
    print()
    print("🚀 يمكنك الآن تشغيل التطبيق:")
    print("   python run.py")
    print()

def fix_common_issues():
    """إصلاح المشاكل الشائعة"""
    print("🛠️ إصلاح المشاكل الشائعة...")
    
    # إصلاح مشكلة الترميز
    try:
        import locale
        locale.setlocale(locale.LC_ALL, 'C.UTF-8')
        print("✅ تم إعداد الترميز")
    except:
        print("⚠️ تعذر إعداد الترميز")
    
    # إصلاح مشكلة المنفذ
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('127.0.0.1', 5000))
    sock.close()
    
    if result == 0:
        print("⚠️ المنفذ 5000 مستخدم")
        print("💡 جرب منفذ آخر أو أوقف التطبيق الآخر")
    else:
        print("✅ المنفذ 5000 متاح")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "fix":
            fix_common_issues()
        elif sys.argv[1] == "templates":
            fix_template_errors()
        elif sys.argv[1] == "sample":
            create_sample_data()
        else:
            print("الاستخدام:")
            print("  python fix_errors.py          # تشخيص شامل")
            print("  python fix_errors.py fix      # إصلاح المشاكل الشائعة")
            print("  python fix_errors.py templates # إصلاح القوالب")
            print("  python fix_errors.py sample   # إنشاء بيانات تجريبية")
    else:
        run_diagnostics()
