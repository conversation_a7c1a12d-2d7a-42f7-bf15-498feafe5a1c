import customtkinter as ctk
from datetime import datetime
import tkinter.messagebox as messagebox
from database import DatabaseManager

# إعداد المظهر
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class SalaryInputWindow:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        
    def open_window(self):
        """فتح نافذة إدخال الراتب"""
        if self.window is None or not self.window.winfo_exists():
            self.window = ctk.CTkToplevel(self.parent)
            self.window.title("إدخال الراتب الشهري")
            self.window.geometry("500x600")
            self.window.resizable(False, False)
            
            # جعل النافذة في المقدمة
            self.window.transient(self.parent)
            self.window.grab_set()
            
            self.create_widgets()
        else:
            self.window.focus()
    
    def create_widgets(self):
        """إنشاء عناصر واجهة إدخال الراتب"""
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.window, 
            text="إدخال الراتب الشهري", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # إطار المحتوى الرئيسي
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(pady=20, padx=20, fill="both", expand=True)
        
        # الراتب الأول
        first_salary_label = ctk.CTkLabel(
            main_frame, 
            text="الراتب الأول (دينار عراقي):", 
            font=ctk.CTkFont(size=16)
        )
        first_salary_label.pack(pady=(20, 5))
        
        self.first_salary_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="أدخل مبلغ الراتب الأول",
            font=ctk.CTkFont(size=14),
            width=300,
            height=40
        )
        self.first_salary_entry.pack(pady=5)
        
        # الراتب الثاني
        second_salary_label = ctk.CTkLabel(
            main_frame, 
            text="الراتب الثاني (اختياري):", 
            font=ctk.CTkFont(size=16)
        )
        second_salary_label.pack(pady=(20, 5))
        
        self.second_salary_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="أدخل مبلغ الراتب الثاني (إن وجد)",
            font=ctk.CTkFont(size=14),
            width=300,
            height=40
        )
        self.second_salary_entry.pack(pady=5)
        
        # الشهر والسنة
        date_frame = ctk.CTkFrame(main_frame)
        date_frame.pack(pady=20, fill="x", padx=20)
        
        # الشهر
        month_label = ctk.CTkLabel(date_frame, text="الشهر:", font=ctk.CTkFont(size=14))
        month_label.pack(side="left", padx=10)
        
        months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
        
        self.month_combo = ctk.CTkComboBox(
            date_frame,
            values=months,
            width=120
        )
        self.month_combo.set(months[datetime.now().month - 1])
        self.month_combo.pack(side="left", padx=10)
        
        # السنة
        year_label = ctk.CTkLabel(date_frame, text="السنة:", font=ctk.CTkFont(size=14))
        year_label.pack(side="left", padx=10)
        
        self.year_entry = ctk.CTkEntry(date_frame, width=80)
        self.year_entry.insert(0, str(datetime.now().year))
        self.year_entry.pack(side="left", padx=10)
        
        # ملاحظات
        notes_label = ctk.CTkLabel(
            main_frame, 
            text="ملاحظات (اختياري):", 
            font=ctk.CTkFont(size=16)
        )
        notes_label.pack(pady=(20, 5))
        
        self.notes_text = ctk.CTkTextbox(
            main_frame,
            height=80,
            width=300
        )
        self.notes_text.pack(pady=5)
        
        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(pady=20, fill="x", padx=20)
        
        save_button = ctk.CTkButton(
            buttons_frame,
            text="حفظ الراتب",
            command=self.save_salary,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=40,
            width=120
        )
        save_button.pack(side="left", padx=10)
        
        clear_button = ctk.CTkButton(
            buttons_frame,
            text="مسح البيانات",
            command=self.clear_fields,
            font=ctk.CTkFont(size=16),
            height=40,
            width=120,
            fg_color="gray"
        )
        clear_button.pack(side="left", padx=10)
        
        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=self.window.destroy,
            font=ctk.CTkFont(size=16),
            height=40,
            width=120,
            fg_color="red"
        )
        cancel_button.pack(side="right", padx=10)
    
    def save_salary(self):
        """حفظ بيانات الراتب"""
        try:
            # التحقق من صحة البيانات
            first_salary_text = self.first_salary_entry.get().strip()
            if not first_salary_text:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ الراتب الأول")
                return
            
            first_salary = float(first_salary_text.replace(",", ""))
            
            second_salary_text = self.second_salary_entry.get().strip()
            second_salary = float(second_salary_text.replace(",", "")) if second_salary_text else 0
            
            # الحصول على الشهر والسنة
            month = self.month_combo.get()
            months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                     "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
            month_num = months.index(month) + 1
            
            year = int(self.year_entry.get())
            notes = self.notes_text.get("1.0", "end-1c")
            
            # حفظ في قاعدة البيانات
            salary_id = self.db_manager.add_salary(
                first_salary, second_salary, month_num, year, notes
            )
            
            # رسالة نجاح
            total = first_salary + second_salary
            messagebox.showinfo(
                "تم الحفظ بنجاح", 
                f"تم حفظ الراتب بنجاح!\n"
                f"الراتب الأول: {first_salary:,.0f} دينار\n"
                f"الراتب الثاني: {second_salary:,.0f} دينار\n"
                f"المجموع: {total:,.0f} دينار\n"
                f"الشهر: {month} {year}"
            )
            
            self.clear_fields()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للرواتب")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")
    
    def clear_fields(self):
        """مسح جميع الحقول"""
        self.first_salary_entry.delete(0, "end")
        self.second_salary_entry.delete(0, "end")
        self.notes_text.delete("1.0", "end")
        self.month_combo.set(["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                             "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"][datetime.now().month - 1])
        self.year_entry.delete(0, "end")
        self.year_entry.insert(0, str(datetime.now().year))


class MainApplication:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("مصروف - إدارة الرواتب والمصروفات الشخصية")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # إنشاء مدير قاعدة البيانات
        self.db_manager = DatabaseManager()

        # إنشاء نافذة إدخال الراتب
        self.salary_window = SalaryInputWindow(self.root, self.db_manager)

        self.create_main_interface()

    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # شريط العنوان
        header_frame = ctk.CTkFrame(self.root, height=80)
        header_frame.pack(fill="x", padx=10, pady=10)
        header_frame.pack_propagate(False)

        title_label = ctk.CTkLabel(
            header_frame,
            text="مصروف - إدارة الرواتب والمصروفات الشخصية",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(expand=True)

        # الإطار الرئيسي
        main_container = ctk.CTkFrame(self.root)
        main_container.pack(fill="both", expand=True, padx=10, pady=10)

        # الشريط الجانبي للقوائم
        sidebar = ctk.CTkFrame(main_container, width=250)
        sidebar.pack(side="left", fill="y", padx=(10, 5), pady=10)
        sidebar.pack_propagate(False)

        # عنوان الشريط الجانبي
        sidebar_title = ctk.CTkLabel(
            sidebar,
            text="القوائم الرئيسية",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        sidebar_title.pack(pady=20)

        # أزرار القوائم
        income_button = ctk.CTkButton(
            sidebar,
            text="📊 إدخال الراتب",
            command=self.salary_window.open_window,
            font=ctk.CTkFont(size=16),
            height=50,
            width=200
        )
        income_button.pack(pady=10)

        expenses_button = ctk.CTkButton(
            sidebar,
            text="💰 إدارة المصروفات",
            command=self.open_expenses,
            font=ctk.CTkFont(size=16),
            height=50,
            width=200
        )
        expenses_button.pack(pady=10)

        debts_button = ctk.CTkButton(
            sidebar,
            text="📋 إدارة الديون",
            command=self.open_debts,
            font=ctk.CTkFont(size=16),
            height=50,
            width=200
        )
        debts_button.pack(pady=10)

        reports_button = ctk.CTkButton(
            sidebar,
            text="📈 التقارير والتحليل",
            command=self.open_reports,
            font=ctk.CTkFont(size=16),
            height=50,
            width=200
        )
        reports_button.pack(pady=10)

        # المنطقة الرئيسية للمحتوى
        content_area = ctk.CTkFrame(main_container)
        content_area.pack(side="right", fill="both", expand=True, padx=(5, 10), pady=10)

        self.create_dashboard(content_area)

    def create_dashboard(self, parent):
        """إنشاء لوحة المعلومات الرئيسية"""
        # عنوان لوحة المعلومات
        dashboard_title = ctk.CTkLabel(
            parent,
            text="لوحة المعلومات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        dashboard_title.pack(pady=20)

        # إطار الإحصائيات
        stats_frame = ctk.CTkFrame(parent)
        stats_frame.pack(fill="x", padx=20, pady=10)

        # الحصول على آخر راتب
        latest_salary = self.db_manager.get_latest_salary()

        if latest_salary:
            salary_info = ctk.CTkLabel(
                stats_frame,
                text=f"آخر راتب مُسجل: {latest_salary[3]:,.0f} دينار عراقي\n"
                     f"الراتب الأول: {latest_salary[1]:,.0f} دينار\n"
                     f"الراتب الثاني: {latest_salary[2]:,.0f} دينار\n"
                     f"الشهر: {latest_salary[4]}/{latest_salary[5]}",
                font=ctk.CTkFont(size=16),
                justify="right"
            )
        else:
            salary_info = ctk.CTkLabel(
                stats_frame,
                text="لم يتم إدخال أي راتب بعد\nاضغط على 'إدخال الراتب' لبدء التسجيل",
                font=ctk.CTkFont(size=16),
                justify="center"
            )

        salary_info.pack(pady=20)

        # رسالة ترحيب
        welcome_frame = ctk.CTkFrame(parent)
        welcome_frame.pack(fill="both", expand=True, padx=20, pady=20)

        welcome_text = ctk.CTkLabel(
            welcome_frame,
            text="مرحباً بك في تطبيق مصروف!\n\n"
                 "يمكنك الآن:\n"
                 "• إدخال وتتبع رواتبك الشهرية\n"
                 "• إدارة مصروفاتك اليومية\n"
                 "• متابعة الديون والالتزامات\n"
                 "• عرض التقارير والتحليلات المالية\n\n"
                 "ابدأ بإدخال راتبك الشهري من القائمة الجانبية",
            font=ctk.CTkFont(size=16),
            justify="center"
        )
        welcome_text.pack(expand=True)

    def open_expenses(self):
        """فتح نافذة إدارة المصروفات"""
        messagebox.showinfo("قريباً", "نافذة إدارة المصروفات ستكون متاحة قريباً!")

    def open_debts(self):
        """فتح نافذة إدارة الديون"""
        messagebox.showinfo("قريباً", "نافذة إدارة الديون ستكون متاحة قريباً!")

    def open_reports(self):
        """فتح نافذة التقارير"""
        messagebox.showinfo("قريباً", "نافذة التقارير والتحليل ستكون متاحة قريباً!")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()


if __name__ == "__main__":
    app = MainApplication()
    app.run()
